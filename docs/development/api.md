# API Documentation

This document describes the API endpoints provided by the Notification Service.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:4000/api/v1
```

## Authentication

Currently, the API does not implement authentication. This will be added in a future release.

## Response Format

All API responses follow a standard format:

### Success Response

```json
{
  "data": <response_data>,
  "meta": <metadata>
}
```

Where:
- `data`: The response data (object, array, or null)
- `meta`: Metadata about the response (pagination info, messages, etc.)

### Error Response

```json
{
  "meta": {},
  "errors": [
    {
      "error": "Error message"
    }
  ]
}
```

Where:
- `errors`: An array of error objects

## Health Check

### Get Health Status

```
GET /health
```

Returns the health status of the service.

#### Response

```json
{
  "data": null,
  "meta": {
    "message": "OK"
  }
}
```

#### Status Codes

- `200 OK`: Service is healthy

## Notifications

### Create Notification

```
POST /notifications
```

Creates a new notification.

#### Request Body

```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "description": "New message received",
  "click_action": "OPEN_URL",
  "click_action_url": "https://example.com/messages",
  "is_reminder": false,
  "notif_type_id": "123e4567-e89b-12d3-a456-************",
  "notif_category_id": "123e4567-e89b-12d3-a456-************"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | UUID | Yes | ID of the user receiving the notification |
| `description` | string | Yes | Notification message |
| `click_action` | string | Yes | Action when notification is clicked (OPEN_URL, OPEN_APP) |
| `click_action_url` | string | No | URL to open (required if click_action is OPEN_URL) |
| `is_reminder` | boolean | No | Whether this is a reminder notification |
| `notif_type_id` | UUID | No | Notification type ID |
| `notif_category_id` | UUID | No | Notification category ID |

#### Response

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "read_at": null,
    "is_reminder": false,
    "notif_type_id": "123e4567-e89b-12d3-a456-************",
    "notif_category_id": "123e4567-e89b-12d3-a456-************",
    "created_at": "2025-04-28T14:30:00Z",
    "updated_at": "2025-04-28T14:30:00Z"
  },
  "meta": {}
}
```

#### Status Codes

- `201 Created`: Notification created successfully
- `400 Bad Request`: Invalid request body
- `500 Internal Server Error`: Server error

### Get All Notifications

```
GET /notifications
```

Retrieves all notifications with pagination.

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number |
| `limit` | integer | No | 10 | Number of items per page (max 100) |

#### Response

```json
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "description": "New message received",
      "click_action": "OPEN_URL",
      "click_action_url": "https://example.com/messages",
      "read_at": null,
      "is_reminder": false,
      "notif_type_id": "123e4567-e89b-12d3-a456-************",
      "notif_category_id": "123e4567-e89b-12d3-a456-************",
      "created_at": "2025-04-28T14:30:00Z",
      "updated_at": "2025-04-28T14:30:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_page": 1
  }
}
```

#### Status Codes

- `200 OK`: Notifications retrieved successfully
- `500 Internal Server Error`: Server error

### Get Notification by ID

```
GET /notifications/{id}
```

Retrieves a notification by ID.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | UUID | Yes | Notification ID |

#### Response

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "read_at": null,
    "is_reminder": false,
    "notif_type_id": "123e4567-e89b-12d3-a456-************",
    "notif_category_id": "123e4567-e89b-12d3-a456-************",
    "created_at": "2025-04-28T14:30:00Z",
    "updated_at": "2025-04-28T14:30:00Z"
  },
  "meta": {}
}
```

#### Status Codes

- `200 OK`: Notification retrieved successfully
- `400 Bad Request`: Invalid notification ID
- `404 Not Found`: Notification not found
- `500 Internal Server Error`: Server error

## Error Codes

The API uses standard HTTP status codes to indicate the success or failure of a request:

| Status Code | Description |
|-------------|-------------|
| `200 OK` | The request was successful |
| `201 Created` | The resource was successfully created |
| `400 Bad Request` | The request was invalid or cannot be served |
| `404 Not Found` | The requested resource does not exist |
| `500 Internal Server Error` | An error occurred on the server |

## API Implementation

The API is implemented using the following components:

### Handlers

Handlers are responsible for processing HTTP requests and returning responses:

```go
// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
    CreateNotification(w http.ResponseWriter, r *http.Request)
    GetNotificationByID(w http.ResponseWriter, r *http.Request)
    GetNotifications(w http.ResponseWriter, r *http.Request)
}
```

### Routes

Routes are defined in `internal/server/route.go`:

```go
func (r *Router) registerV1Route(chiRouter chi.Router) {
    chiRouter.Route("/v1", func(rt chi.Router) {
        // Health routes
        rt.Route("/health", func(rt2 chi.Router) {
            rt2.Get("/", r.AppContext.HealthHandler.HealthCheck)
        })

        // Notification routes
        if r.AppContext.NotificationHandler != nil {
            rt.Route("/notifications", func(rt2 chi.Router) {
                rt2.Post("/", r.AppContext.NotificationHandler.CreateNotification)
                rt2.Get("/", r.AppContext.NotificationHandler.GetNotifications)
                rt2.Get("/{id}", r.AppContext.NotificationHandler.GetNotificationByID)
            })
        }
    })
}
```

## Testing the API

You can test the API using curl or any API client:

### Health Check

```bash
curl http://localhost:4000/api/v1/health
```

### Create Notification

```bash
curl -X POST http://localhost:4000/api/v1/notifications \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "is_reminder": false
  }'
```

### Get All Notifications

```bash
curl http://localhost:4000/api/v1/notifications?page=1&limit=10
```

### Get Notification by ID

```bash
curl http://localhost:4000/api/v1/notifications/123e4567-e89b-12d3-a456-************
```

## Future API Enhancements

The following enhancements are planned for future releases:

1. **Authentication**: Add JWT-based authentication
2. **User-Specific Notifications**: Filter notifications by user ID
3. **Mark as Read**: Add endpoint to mark notifications as read
4. **Notification Types**: Add endpoints to manage notification types
5. **Notification Categories**: Add endpoints to manage notification categories
6. **Websocket Support**: Real-time notification delivery
7. **Bulk Operations**: Batch create and update operations
