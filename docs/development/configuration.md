# Configuration Guide

This document describes the configuration options for the Notification Service.

## Overview

The Notification Service uses [<PERSON>](https://github.com/spf13/viper) for configuration management, which supports multiple configuration sources:

1. Environment variables (via `.env` file)
2. Command-line flags

Configuration is loaded in the following order, with later sources overriding earlier ones:
1. Default values
2. Environment variables from `.env` file
3. System environment variables
4. Command-line flags

## Environment Variables

The application uses a `.env` file in the project root directory to load environment variables. Here's a complete example (`.env.example`):

```
# Application Configuration
APP_NAME=notification-service
APP_ENV=development

# Database Configuration
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hub_test
DATABASE_POOL=10
DATABASE_IDLE_TIMEOUT=1m
DATABASE_CONN_MAX_LIFETIME=15m
DATABASE_CONN_MAX_IDLE_TIME=5m
DATABASE_MAX_OPEN_CONN=10
DATABASE_MAX_IDLE_CONN=5

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_SASL=false
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_PREFIX=
KAFKA_CERT=

# Datadog Configuration
DD_AGENT_HOST=localhost
DD_AGENT_PORT=8126
STATSD_HOST=localhost
STATSD_PORT=8125
DATADOG_TRACER=false
```

To set up your environment:
1. Copy `.env.example` to `.env`
2. Modify the values in `.env` as needed
3. The application will automatically load these values at startup

## Environment Variable Naming

The environment variables follow a specific naming convention:

- Sections are separated by underscores
- All characters are uppercase

For example:
- Application name is set with `APP_NAME`
- Database username is set with `DATABASE_USERNAME`
- Kafka brokers are set with `KAFKA_BROKERS`

## Configuration Sections

### Application Configuration

| Config Key | Environment Variable | Type | Default | Description |
|------------|----------------------|------|---------|-------------|
| `app.name` | `APP_NAME` | string | `notification-service` | Application name |
| `app.env` | `APP_ENV` | string | `development` | Application environment (development, staging, production) |

### Database Configuration

| Config Key | Environment Variable | Type | Default | Description |
|------------|----------------------|------|---------|-------------|
| `database.username` | `DATABASE_USERNAME` | string | `postgres` | Database username |
| `database.password` | `DATABASE_PASSWORD` | string | `password` | Database password |
| `database.host` | `DATABASE_HOST` | string | `localhost` | Database host |
| `database.port` | `DATABASE_PORT` | string | `5432` | Database port |
| `database.name` | `DATABASE_NAME` | string | `hub_test` | Database name |
| `database.pool` | `DATABASE_POOL` | int | `10` | Maximum number of database connections |
| `database.idle_timeout` | `DATABASE_IDLE_TIMEOUT` | duration | `1m` | Maximum time a connection can be idle |
| `database.conn_max_lifetime` | `DATABASE_CONN_MAX_LIFETIME` | duration | `15m` | Maximum lifetime of a connection |
| `database.conn_max_idle_time` | `DATABASE_CONN_MAX_IDLE_TIME` | duration | `5m` | Maximum idle time of a connection |
| `database.max_open_conn` | `DATABASE_MAX_OPEN_CONN` | int | `10` | Maximum number of open connections |
| `database.max_idle_conn` | `DATABASE_MAX_IDLE_CONN` | int | `5` | Maximum number of idle connections |

### Kafka Configuration

| Config Key | Environment Variable | Type | Default | Description |
|------------|----------------------|------|---------|-------------|
| `kafka.brokers` | `KAFKA_BROKERS` | []string | `["localhost:9092"]` | List of Kafka broker addresses |
| `kafka.sasl` | `KAFKA_SASL` | bool | `false` | Enable SASL authentication |
| `kafka.username` | `KAFKA_USERNAME` | string | `""` | SASL username |
| `kafka.password` | `KAFKA_PASSWORD` | string | `""` | SASL password |
| `kafka.prefix` | `KAFKA_PREFIX` | string | `""` | Topic prefix |
| `kafka.cert` | `KAFKA_CERT` | string | `""` | TLS certificate for Kafka |

### Datadog Configuration

| Config Key | Environment Variable | Type | Default | Description |
|------------|----------------------|------|---------|-------------|
| `dd.agent.host` | `DD_AGENT_HOST` | string | `localhost` | Datadog agent host |
| `dd.agent.port` | `DD_AGENT_PORT` | string | `8126` | Datadog agent port |
| `statsd.host` | `STATSD_HOST` | string | `localhost` | StatsD host |
| `statsd.port` | `STATSD_PORT` | string | `8125` | StatsD port |
| `datadog_tracer` | `DATADOG_TRACER` | bool | `false` | Enable Datadog tracer |

## Command-Line Flags

The application supports the following command-line flags:

### Server Command

```bash
notification-service server [flags]
```

| Flag | Short | Type | Default | Description |
|------|-------|------|---------|-------------|
| `--server` | `-s` | string | `:4000` | Server address |

Example:
```bash
notification-service server -s :8080
```

### Worker Command

```bash
notification-service worker [flags]
```

| Flag | Short | Type | Default | Description |
|------|-------|------|---------|-------------|
| `--topic` | `-t` | string | `""` | Kafka topic to consume |
| `--group` | `-g` | string | `""` | Kafka consumer group ID |

Example:
```bash
notification-service worker -t notifications -g notification-consumers
```

### Version Command

```bash
notification-service version
```

Displays version information:
- Build Date
- Git Commit
- Version
- Environment
- Go Version
- OS / Arch

## Configuration Loading

Configuration is loaded in the `config.Init()` function:

```go
func Init() {
    // Load .env file if it exists
    if err := godotenv.Load(); err != nil {
        stdLog.Printf("No .env file found, using system environment variables")
    }

    // Configure Viper to read from environment variables
    viper.AutomaticEnv()

    // Set default values
    setDefaults()
}

func setDefaults() {
    // Application defaults
    viper.SetDefault("APP_NAME", "notification-service")
    viper.SetDefault("APP_ENV", "development")

    // Database defaults
    viper.SetDefault("DATABASE_POOL", 10)
    viper.SetDefault("DATABASE_IDLE_TIMEOUT", "1m")
    // ... other defaults
}
```

## Configuration Access

Configuration values are accessed through getter functions in the `config` package:

```go
// Application configuration
func GetAppName() string {
    return viper.GetString("app.name")
}

func GetAppEnv() string {
    return viper.GetString("app.env")
}

// Database configuration
func GetDatabaseDsn(prefix string) string {
    username := viper.GetString("database.username")
    pass := url.QueryEscape(viper.GetString("database.password"))
    host := viper.GetString("database.host")
    port := viper.GetString("database.port")
    dbName := viper.GetString("database.name")

    return fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", username, pass, host, port, dbName)
}

// Kafka configuration
func GetKafkaBrokers() []string {
    return viper.GetStringSlice("kafka.brokers")
}
```

## Best Practices

1. **Sensitive Information**:
   - Don't commit sensitive information (passwords, API keys) to version control
   - Use environment variables for sensitive values in production
   - Use placeholder values in example configuration files

2. **Environment-Specific Configuration**:
   - Use the `app.env` setting to load environment-specific configuration
   - Consider using separate configuration files for different environments

3. **Validation**:
   - Validate configuration values at startup
   - Provide meaningful error messages for missing or invalid configuration

4. **Documentation**:
   - Document all configuration options
   - Include example configuration files
   - Explain the purpose and valid values for each option

## Troubleshooting

### Environment File Not Found

If you see the message `No .env file found, using system environment variables`, it means the application couldn't find the `.env` file in the current directory.

Solutions:
1. Create a `.env` file in the project root (copy from `.env.example`)
2. Set system environment variables for all required configuration
3. Run the application from the project root directory

### Database Connection Issues

If the application fails to connect to the database, check:
1. Database configuration in `config.yaml`
2. Database server is running and accessible
3. Database credentials are correct
4. Database name exists

### Kafka Connection Issues

If the application fails to connect to Kafka, check:
1. Kafka configuration in `config.yaml`
2. Kafka server is running and accessible
3. Kafka credentials are correct (if SASL is enabled)
4. Kafka topics exist
