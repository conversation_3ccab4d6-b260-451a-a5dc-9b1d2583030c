# Configuration System Migration: YAML to Environment Variables

This document details the migration from YAML-based configuration to environment variables loaded from a `.env` file.

## Overview

In April 2025, the Notification Service configuration system was updated to use environment variables loaded from a `.env` file instead of a YAML configuration file (`config.yaml`). This change aligns with modern best practices for application configuration.

## Rationale

The migration to environment variables provides several benefits:

1. **Standardization**: Environment variables are a standard approach for configuring applications across different platforms and languages.

2. **Deployment Flexibility**: Environment variables make it easier to deploy the application in different environments, including containerized environments like Kubernetes.

3. **Security**: Environment variables provide better separation of configuration from code, reducing the risk of sensitive information being committed to version control.

4. **Compatibility**: Better compatibility with container orchestration systems, cloud platforms, and CI/CD pipelines.

5. **Simplicity**: Simpler configuration management with a flat structure instead of nested YAML.

## Implementation Details

### Technologies Used

- **[godotenv](https://github.com/joho/godotenv)**: Loads environment variables from a `.env` file
- **[Viper](https://github.com/spf13/viper)**: Manages environment variables and provides defaults

### Key Changes

1. **Configuration Loading**:
   - Before: Loaded configuration from `config.yaml` with fallback to environment variables
   - After: Loads environment variables from `.env` file with fallback to system environment variables

2. **Naming Convention**:
   - Before: Nested keys with dot notation (e.g., `app.name`, `database.username`)
   - After: Uppercase with underscores (e.g., `APP_NAME`, `DATABASE_USERNAME`)

3. **Array Values**:
   - Before: YAML arrays (e.g., `kafka.brokers: [localhost:9092]`)
   - After: Comma-separated values (e.g., `KAFKA_BROKERS=localhost:9092,localhost:9093`)

4. **Default Values**:
   - Before: Default values were set in the code when configuration was missing
   - After: Default values are explicitly set in the `setDefaults()` function

### Code Changes

The `config.Init()` function was updated:

```go
// Before
func Init() {
    viper.AddConfigPath(".")
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")

    viper.AutomaticEnv()
    viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

    err := viper.ReadInConfig()
    if err != nil {
        if _, ok := err.(viper.ConfigFileNotFoundError); ok {
            stdLog.Printf("No config file found, get config from env var.")
        } else {
            stdLog.Fatalf("error reading config file: %v", err)
        }
    }
}

// After
func Init() {
    // Load .env file if it exists
    if err := godotenv.Load(); err != nil {
        stdLog.Printf("No .env file found, using system environment variables")
    }

    // Configure Viper to read from environment variables
    viper.AutomaticEnv()
    
    // Set default values
    setDefaults()
}

func setDefaults() {
    // Application defaults
    viper.SetDefault("APP_NAME", "notification-service")
    viper.SetDefault("APP_ENV", "development")
    
    // Database defaults
    viper.SetDefault("DATABASE_POOL", 10)
    viper.SetDefault("DATABASE_IDLE_TIMEOUT", "1m")
    viper.SetDefault("DATABASE_CONN_MAX_LIFETIME", "15m")
    viper.SetDefault("DATABASE_CONN_MAX_IDLE_TIME", "5m")
    viper.SetDefault("DATABASE_MAX_OPEN_CONN", 10)
    viper.SetDefault("DATABASE_MAX_IDLE_CONN", 5)
}
```

Getter functions were also updated to use the new naming convention:

```go
// Before
func GetAppName() string {
    return viper.GetString("app.name")
}

// After
func GetAppName() string {
    return viper.GetString("APP_NAME")
}
```

## Migration Guide

If you're updating from a previous version that used `config.yaml`, follow these steps:

1. **Create a `.env` file**:
   ```bash
   cp .env.example .env
   ```

2. **Convert your configuration**:
   - For each key in your `config.yaml`, convert it to the new format
   - Replace dots with underscores and convert to uppercase
   - For arrays, use comma-separated values

3. **Update custom code**:
   - If you've added custom configuration keys, update the getter functions
   - If you've added custom default values, add them to the `setDefaults()` function

4. **Remove `config.yaml`**:
   - Once you've migrated all configuration, you can remove the `config.yaml` file

## Example Conversion

### Before (`config.yaml`)

```yaml
app:
  name: notification-service
  env: development

database:
  username: postgres
  password: password
  host: localhost
  port: 5432
  name: hub_test
  pool: 10
  idle_timeout: 1m

kafka:
  brokers:
    - localhost:9092
    - localhost:9093
  sasl: false
  username: ""
  password: ""
  prefix: ""
```

### After (`.env`)

```
APP_NAME=notification-service
APP_ENV=development

DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hub_test
DATABASE_POOL=10
DATABASE_IDLE_TIMEOUT=1m

KAFKA_BROKERS=localhost:9092,localhost:9093
KAFKA_SASL=false
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_PREFIX=
```

## Troubleshooting

### Environment File Not Found

If you see the message `No .env file found, using system environment variables`, it means the application couldn't find the `.env` file in the current directory.

Solutions:
1. Create a `.env` file in the project root (copy from `.env.example`)
2. Set system environment variables for all required configuration
3. Run the application from the project root directory

### Missing Configuration Values

If the application fails to start due to missing configuration values:

1. Check your `.env` file for missing or incorrect values
2. Verify that the environment variables are correctly named
3. Check if the default values in `setDefaults()` are appropriate for your environment

## Future Improvements

Potential future improvements to the configuration system:

1. **Validation**: Add validation for configuration values at startup
2. **Documentation**: Generate documentation for all configuration options
3. **Secrets Management**: Integrate with a secrets management system
4. **Dynamic Reloading**: Support for reloading configuration without restarting the application
