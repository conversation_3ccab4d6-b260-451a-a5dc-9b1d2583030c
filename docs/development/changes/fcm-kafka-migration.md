# FCM Notification System Migration: REST API to Kafka Consumer

This document details the migration of the FCM (Firebase Cloud Messaging) notification system from a REST API to a Kafka consumer.

## Overview

In May 2025, the Notification Service's FCM notification system was updated to use a Kafka consumer instead of a REST API. This change aligns with the service's event-driven architecture and improves scalability and reliability.

## Rationale

The migration to a Kafka consumer provides several benefits:

1. **Event-Driven Architecture**: Better integration with event-driven systems, allowing for more flexible and decoupled communication between services.

2. **Decoupling**: Separates the notification sending process from the API layer, reducing dependencies and allowing each component to scale independently.

3. **Scalability**: Allows for better scaling of notification processing by distributing the workload across multiple consumer instances.

4. **Reliability**: Provides message persistence and retry capabilities, ensuring notifications are not lost even if the service is temporarily unavailable.

5. **Asynchronous Processing**: Enables asynchronous processing of notifications, improving response times for client applications.

## Implementation Details

### Technologies Used

- **Kafka**: Message broker for notification events
- **Sarama**: Go client library for Kafka
- **Firebase Admin SDK**: For sending FCM notifications

### Key Changes

1. **New Kafka Topic**:
   - Added a new Kafka topic `fcm-notifications` for FCM notification events

2. **FCM Consumer Implementation**:
   - Created a new Kafka consumer for FCM notifications
   - Implemented message parsing and validation
   - Integrated with the existing PushNotificationService

3. **Worker Configuration**:
   - Updated the worker to support the new FCM consumer
   - Added configuration for the FCM consumer

### Code Changes

#### 1. Added FCM Topic Constant

```go
// internal/worker/contract.go
package worker

const (
    FCMTopic = "fcm-notifications"
)
```

#### 2. Implemented FCM Consumer

```go
// internal/app/consumer/fcm_consumer.go
package consumer

import (
    "context"
    "encoding/json"
    "log/slog"

    "bitbucket.org/terbang-ventures/notification-service/internal/app/model"
    "bitbucket.org/terbang-ventures/notification-service/internal/app/service"
    "github.com/IBM/sarama"
    "github.com/google/uuid"
)

// FCMNotificationMessage represents the message structure for FCM notifications
type FCMNotificationMessage struct {
    SSOIDs      []uuid.UUID             `json:"sso_ids"`
    Data        map[string]interface{}  `json:"data"`
    Notification map[string]string      `json:"notification"`
    ClickAction string                  `json:"click_action"`
}

// FCMConsumer is a Kafka consumer for FCM notifications
type FCMConsumer struct {
    pushNotificationService service.PushNotificationService
}

// NewFCMConsumer creates a new FCM consumer
func NewFCMConsumer(pushNotificationService service.PushNotificationService) *FCMConsumer {
    return &FCMConsumer{
        pushNotificationService: pushNotificationService,
    }
}

// ConsumeClaim is called for each consumer session to consume messages
func (c *FCMConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
    for {
        select {
        case message, ok := <-claim.Messages():
            if !ok {
                return nil
            }

            // Process the message
            if err := c.processMessage(session.Context(), message.Value); err != nil {
                slog.ErrorContext(session.Context(), "Error processing FCM notification message",
                    slog.Any("error", err),
                    slog.String("value", string(message.Value)),
                )
            }

            // Mark the message as processed
            session.MarkMessage(message, "")
        case <-session.Context().Done():
            return nil
        }
    }
}

// processMessage processes an FCM notification message
func (c *FCMConsumer) processMessage(ctx context.Context, messageValue []byte) error {
    var message FCMNotificationMessage
    if err := json.Unmarshal(messageValue, &message); err != nil {
        return err
    }

    // Send the push notification
    return c.pushNotificationService.SendPushNotification(
        ctx,
        message.SSOIDs,
        message.Data,
        message.Notification,
        message.ClickAction,
    )
}
```

#### 3. Updated Worker Configuration

```go
// cmd/worker/worker.go
func workerRun(cmd *cobra.Command, args []string) error {
    defer closeResource(kafkaConsumerGroup)

    ctx := context.Background()
    var c sarama.ConsumerGroupHandler

    if topic == worker.FCMTopic {
        // Initialize repositories
        fcmTokenRepo := repository.NewFCMTokenRepository(gormDBConn)

        // Initialize FCM service
        fcmService := fcm.NewFCMService()

        // Initialize push notification service
        pushNotificationService := service.NewPushNotificationService(fcmTokenRepo, fcmService)

        // Create FCM consumer
        c = consumer.NewFCMConsumer(pushNotificationService)
    } else {
        stdLog.Fatalf("topic %s is not defined", topic)
    }

    w := worker.NewWorker(topic, kafkaConsumerGroup, c)
    w.Start(ctx)

    return nil
}
```

## Message Format

The FCM notification messages should follow this JSON format:

```json
{
  "sso_ids": ["123e4567-e89b-12d3-a456-************"],
  "data": {
    "key1": "value1",
    "key2": "value2"
  },
  "notification": {
    "title": "Notification Title",
    "body": "Notification Body"
  },
  "click_action": "https://example.com/path"
}
```

## Migration Guide

If you were previously using the REST API to send FCM notifications, follow these steps to migrate to the Kafka consumer:

1. **Update Client Applications**:
   - Instead of making HTTP requests to the `/push-notifications` endpoint, publish messages to the `fcm-notifications` Kafka topic
   - Use the message format described above

2. **Start the FCM Consumer**:
   ```bash
   go run main.go worker -t fcm-notifications -g fcm-notification-consumers
   ```

3. **Test the Integration**:
   - Publish test messages to the Kafka topic
   - Verify that notifications are being sent correctly

## Troubleshooting

### Common Issues

1. **Message Format Errors**:
   - Ensure the message follows the correct JSON format
   - Check that all required fields are present
   - Verify that UUIDs are in the correct format

2. **Kafka Connection Issues**:
   - Verify Kafka is running and accessible
   - Check Kafka configuration in the `.env` file
   - Ensure the topic exists and has the correct permissions

3. **FCM Configuration Issues**:
   - Verify FCM credentials are correctly configured in the `.env` file
   - Check that the FCM project IDs are correct
   - Ensure the credentials files exist and are accessible

## Future Improvements

Potential future improvements to the FCM notification system:

1. **Retry Mechanism**: Implement a retry mechanism for failed notifications
2. **Dead Letter Queue**: Send failed messages to a dead letter queue for later processing
3. **Batch Processing**: Process messages in batches for better performance
4. **Metrics**: Add metrics for monitoring consumer performance
5. **Rate Limiting**: Implement rate limiting to prevent FCM quota exhaustion
