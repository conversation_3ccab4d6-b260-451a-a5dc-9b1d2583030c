# Architecture

This document describes the architecture of the Notification Service, including its components, interactions, and design principles.

## Overview

The Notification Service follows a clean architecture approach, separating concerns into distinct layers with clear boundaries. This design promotes:

- **Testability**: Components can be tested in isolation
- **Maintainability**: Changes in one layer don't affect others
- **Flexibility**: Implementation details can change without affecting business logic
- **Independence**: Business logic is independent of frameworks and external systems

## Architectural Layers

The service is organized into the following layers:

### 1. Presentation Layer (Handlers)

The presentation layer is responsible for handling HTTP requests and responses. It:

- Validates incoming requests
- Calls the appropriate service methods
- Formats responses
- Handles errors

**Location**: `internal/app/handler/`

**Key Components**:
- `HealthHandler`: Provides health check endpoint
- `NotificationHandler`: Handles notification-related endpoints

### 2. Business Logic Layer (Services)

The service layer contains the business logic of the application. It:

- Implements business rules
- Orchestrates data access
- Performs validations
- Handles transactions

**Location**: `internal/app/service/`

**Key Components**:
- `NotificationService`: Implements notification business logic

### 3. Data Access Layer (Repositories)

The repository layer is responsible for data persistence and retrieval. It:

- Abstracts database operations
- Implements CRUD operations
- Maps database entities to domain models

**Location**: `internal/app/repository/`

**Key Components**:
- `NotificationRepository`: Handles notification data access

### 4. Domain Layer (Models)

The domain layer contains the core business entities and logic. It:

- Defines the data structures
- Implements entity-specific business rules
- Provides validation logic

**Location**: `internal/app/model/`

**Key Components**:
- `Notification`: Represents a notification entity

## Component Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        HTTP Requests                            │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Presentation Layer                         │
│                                                                 │
│  ┌─────────────────────┐        ┌──────────────────────────┐    │
│  │   HealthHandler     │        │   NotificationHandler    │    │
│  └─────────────────────┘        └──────────────────────────┘    │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Business Logic Layer                       │
│                                                                 │
│               ┌──────────────────────────────┐                  │
│               │     NotificationService      │                  │
│               └──────────────────────────────┘                  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Data Access Layer                          │
│                                                                 │
│               ┌──────────────────────────────┐                  │
│               │    NotificationRepository    │                  │
│               └──────────────────────────────┘                  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                          Database                               │
└─────────────────────────────────────────────────────────────────┘
```

## Request Flow

1. HTTP request is received by the router (`internal/server/route.go`)
2. Router dispatches the request to the appropriate handler
3. Handler validates the request and calls the service
4. Service executes business logic and calls the repository
5. Repository interacts with the database
6. Response flows back through the layers to the client

## Dependency Injection

The application uses manual dependency injection to wire components together:

```go
// Initialize repositories
notificationRepo := repository.NewNotificationRepository(gormDBConn)

// Initialize services
notificationService := service.NewNotificationService(notificationRepo)

// Initialize handlers
notificationHandler := handler.NewNotificationHandler(notificationService)

// Create application context
appContext := server.AppContext{
    HealthHandler:       healthHandler,
    NotificationHandler: notificationHandler,
}

// Create and start API server
apiServer := server.NewAPIServer(address, &appContext)
apiServer.Start(ctx)
```

This approach:
- Makes dependencies explicit
- Facilitates testing through mocking
- Allows for flexible component configuration

## Interface-Based Design

The application uses interfaces to define component contracts:

```go
// Service interface
type NotificationService interface {
    CreateNotification(ctx context.Context, notification *model.Notification) error
    GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.Notification, error)
    GetNotifications(ctx context.Context, page, limit int) ([]model.Notification, int64, error)
}

// Repository interface
type NotificationRepository interface {
    Create(ctx context.Context, notification *model.Notification) error
    FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error)
    FindAll(ctx context.Context, page, limit int) ([]model.Notification, int64, error)
}
```

Benefits of this approach:
- Components depend on abstractions, not implementations
- Easy to mock dependencies for testing
- Implementation details can change without affecting clients

## Configuration Management

The application uses Viper and godotenv for configuration management:

```go
func Init() {
    // Load .env file if it exists
    if err := godotenv.Load(); err != nil {
        stdLog.Printf("No .env file found, using system environment variables")
    }

    // Configure Viper to read from environment variables
    viper.AutomaticEnv()

    // Set default values
    setDefaults()
}

func setDefaults() {
    // Application defaults
    viper.SetDefault("APP_NAME", "notification-service")
    viper.SetDefault("APP_ENV", "development")

    // Database defaults
    viper.SetDefault("DATABASE_POOL", 10)
    viper.SetDefault("DATABASE_IDLE_TIMEOUT", "1m")
    // ... other defaults
}
```

This provides:
- Configuration from environment variables (via .env file)
- Type-safe configuration access
- Default values for missing configuration

## Error Handling

The application uses a structured approach to error handling:

1. **Domain Errors**: Defined in the domain layer
2. **Service Errors**: Translated from technical errors to domain errors
3. **Handler Errors**: Translated from domain errors to HTTP responses

Example:
```go
// Handler error handling
func (h *notificationHandler) GetNotificationByID(w http.ResponseWriter, r *http.Request) {
    idParam := chi.URLParam(r, "id")
    id, err := uuid.Parse(idParam)
    if err != nil {
        httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
        return
    }

    notification, err := h.notificationService.GetNotificationByID(r.Context(), id)
    if err != nil {
        httpLib.Error(w, err)
        return
    }

    httpLib.WriteSuccessResponse(w, http.StatusOK, notification, nil)
}
```

## Logging

The application uses structured logging with `log/slog`:

```go
func InitAppLogger(cfg LoggerConf) {
    log, err := logger.SlogOption{
        Resource: map[string]string{
            "service.name": cfg.ServiceName,
            "service.env":  cfg.ServiceEnvironment,
        },
    }.NewSlog()
    if err != nil {
        err = fmt.Errorf("prepare logger error: %w", err)
        stdlog.Fatal(err)
        return
    }

    slog.SetDefault(log)
}
```

Benefits:
- Structured logs for easier parsing and analysis
- Consistent log format across the application
- Context-aware logging

## Command-Line Interface

The application uses Cobra for command-line interface:

```go
var RootCmd = &cobra.Command{
    Use:   "notification-service",
    Short: "Broadcast service",
    Long:  "Qontak chat broadcast service",
}

func Execute() {
    if err := RootCmd.Execute(); err != nil {
        _ = RootCmd.Help()
        fmt.Println(err)
        os.Exit(1)
    }
}

func init() {
    RootCmd.AddCommand(server.ServerCmd)
    RootCmd.AddCommand(worker.WorkerCmd)
    RootCmd.AddCommand(VersionCmd)
}
```

This provides:
- Structured command hierarchy
- Automatic help generation
- Flag parsing and validation

## Database Access

The application uses GORM for database access:

```go
func InitDatabase(cfg dbLib.Config) *gorm.DB {
    gormDBConn, err := dbLib.InitGORM(dbLib.POSTGRESQL, dbLib.GORMConfig{
        Config: cfg,
    })
    if err != nil {
        stdLog.Printf("error while init gorm database connection: %v", err)
        return nil
    }

    return gormDBConn
}
```

Benefits:
- ORM for mapping between Go structs and database tables
- Migration support
- Query building
- Connection pooling

## Message Queue Integration

The application integrates with Kafka for asynchronous messaging:

```go
func InitKafkaSyncProducer(cfg kafka.Config) kafka.Producer {
    producer, err := kafka.NewSyncProducer(cfg)
    if err != nil {
        slog.Error("error while init kafka sync producer", slog.Any("error", err))
        return nil
    }

    return producer
}
```

This enables:
- Event-driven architecture
- Asynchronous processing
- System integration
- Scalability

## Graceful Shutdown

The application implements graceful shutdown to ensure resources are properly released:

```go
func (s *APIServer) gracefulShutdown(ctx context.Context, quit chan os.Signal) {
    signal.Notify(
        quit,
        os.Interrupt,
        syscall.SIGHUP,
        syscall.SIGINT,
        syscall.SIGTERM,
        syscall.SIGQUIT,
    )

    q := <-quit

    slog.WarnContext(context.Background(), "shutting down server ...",
        slog.Any("signal", q),
    )

    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()

    s.httpServer.SetKeepAlivesEnabled(false)
    if err := s.httpServer.Shutdown(ctx); err != nil {
        slog.ErrorContext(context.Background(), "error while shutting down server",
            slog.Any("error", err),
        )
    }

    slog.Info("server exiting")
}
```

This ensures:
- In-flight requests are completed
- Resources are properly released
- Clean application shutdown
