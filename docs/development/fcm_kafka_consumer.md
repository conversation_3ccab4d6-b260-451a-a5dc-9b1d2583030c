# FCM Kafka Consumer

This document describes the FCM Kafka consumer implementation in the Notification Service.

## Overview

The FCM Kafka consumer allows the Notification Service to send push notifications to mobile and web clients using Firebase Cloud Messaging (FCM) based on messages received from a Kafka topic. This replaces the previous REST API approach with an event-driven architecture.

## Architecture

The FCM Kafka consumer follows the same architecture as other Kafka consumers in the application:

1. **Consumer Implementation**: Implements the Sarama ConsumerGroupHandler interface
2. **Message Processing**: Parses and validates incoming messages
3. **Service Integration**: Uses the PushNotificationService to send notifications

## Configuration

### Kafka Topic

The FCM notifications are consumed from the `fcm-notifications` topic. This is defined as a constant in `internal/worker/contract.go`:

```go
const (
    FCMTopic = "fcm-notifications"
)
```

### FCM Configuration

FCM configuration is stored in the `.env` file:

```
# FCM Configuration
FCM_CRM_PROJECT_ID=crm-project-id
FCM_CRM_CREDENTIALS_FILE=/path/to/crm-credentials.json
FCM_CHAT_PROJECT_ID=chat-project-id
FCM_CHAT_CREDENTIALS_FILE=/path/to/chat-credentials.json
FCM_DEFAULT_PROJECT_ID=default-project-id
FCM_DEFAULT_CREDENTIALS_FILE=/path/to/default-credentials.json
```

## Message Format

The FCM notification messages should follow this JSON format:

```json
{
  "sso_ids": ["123e4567-e89b-12d3-a456-************"],
  "data": {
    "key1": "value1",
    "key2": "value2"
  },
  "notification": {
    "title": "Notification Title",
    "body": "Notification Body"
  },
  "click_action": "https://example.com/path"
}
```

### Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `sso_ids` | array of UUIDs | Yes | IDs of the users to receive the notification |
| `data` | object | No | Custom data to include in the notification |
| `notification` | object | Yes | Notification content (title, body, etc.) |
| `click_action` | string | No | Action when notification is clicked (URL, app action) |

## Implementation

### FCM Consumer

The FCM consumer is implemented in `internal/app/consumer/fcm_consumer.go`:

```go
// FCMConsumer is a Kafka consumer for FCM notifications
type FCMConsumer struct {
    pushNotificationService service.PushNotificationService
}

// NewFCMConsumer creates a new FCM consumer
func NewFCMConsumer(pushNotificationService service.PushNotificationService) *FCMConsumer {
    return &FCMConsumer{
        pushNotificationService: pushNotificationService,
    }
}
```

### Message Processing

The consumer processes messages in the `ConsumeClaim` method:

```go
// ConsumeClaim is called for each consumer session to consume messages
func (c *FCMConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
    for {
        select {
        case message, ok := <-claim.Messages():
            if !ok {
                slog.Info("message channel was closed")
                return nil
            }

            // Process the message
            if err := c.processMessage(session.Context(), message.Value); err != nil {
                slog.ErrorContext(session.Context(), "Error processing FCM notification message",
                    slog.Any("error", err),
                    slog.String("value", string(message.Value)),
                )
                // Continue processing other messages even if one fails
            }

            // Mark the message as processed
            session.MarkMessage(message, "")
        case <-session.Context().Done():
            return nil
        }
    }
}
```

### Worker Configuration

The worker is configured in `cmd/worker/worker.go`:

```go
if topic == worker.FCMTopic {
    // Initialize repositories
    fcmTokenRepo := repository.NewFCMTokenRepository(gormDBConn)

    // Initialize FCM service
    fcmService := fcm.NewFCMService()

    // Initialize push notification service
    pushNotificationService := service.NewPushNotificationService(fcmTokenRepo, fcmService)

    // Create FCM consumer
    c = consumer.NewFCMConsumer(pushNotificationService)
} else {
    stdLog.Fatalf("topic %s is not defined", topic)
}
```

## Running the Consumer

To run the FCM consumer, use the following command:

```bash
make run ARGS="worker -t fcm-notifications -g fcm-notification-consumers"
```

Or directly:

```bash
go run main.go worker -t fcm-notifications -g fcm-notification-consumers
```

## Testing

### Producing Test Messages

You can produce test messages using the Kafka console producer:

```bash
kafka-console-producer --bootstrap-server localhost:9092 --topic fcm-notifications
```

Then enter a message in the required JSON format:

```json
{"sso_ids":["123e4567-e89b-12d3-a456-************"],"notification":{"title":"Test Title","body":"Test Body"},"click_action":"https://example.com"}
```

### Monitoring

You can monitor the consumer using the Kafka consumer groups command:

```bash
kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group fcm-notification-consumers
```

## Error Handling

The FCM consumer implements the following error handling strategies:

1. **Message Validation**: Validates incoming messages and logs warnings for invalid messages
2. **Continued Processing**: Continues processing other messages even if one fails
3. **Detailed Logging**: Logs detailed error information for troubleshooting

## Future Improvements

Potential future improvements to the FCM consumer:

1. **Retry Mechanism**: Implement a retry mechanism for failed notifications
2. **Dead Letter Queue**: Send failed messages to a dead letter queue for later processing
3. **Batch Processing**: Process messages in batches for better performance
4. **Metrics**: Add metrics for monitoring consumer performance
