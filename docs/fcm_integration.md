# Firebase Cloud Messaging (FCM) Integration

This document describes the Firebase Cloud Messaging (FCM) integration in the Notification Service.

## Overview

The FCM integration allows the Notification Service to send push notifications to mobile and web clients using Firebase Cloud Messaging. It supports sending notifications to users from different sources (CRM, Chat, etc.) using different FCM credentials.

## Configuration

FCM configuration is stored in the `.env` file:

```
# Firebase Cloud Messaging Configuration
# CRM FCM Configuration
FCM_CRM_PROJECT_ID=your-crm-project-id
FCM_CRM_CREDENTIALS_FILE=/path/to/crm-credentials.json
# Chat FCM Configuration
FCM_CHAT_PROJECT_ID=your-chat-project-id
FCM_CHAT_CREDENTIALS_FILE=/path/to/chat-credentials.json
# Default FCM Configuration
FCM_DEFAULT_PROJECT_ID=your-default-project-id
FCM_DEFAULT_CREDENTIALS_FILE=/path/to/default-credentials.json
```

## Database Schema

FCM tokens are stored in the `fcm_tokens` table:

```sql
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sso_id UUID NOT NULL,
    token TEXT NOT NULL,
    user_type VARCHAR(50) NOT NULL,
    user_source VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_fcm_tokens_sso_id ON fcm_tokens(sso_id);
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_token ON fcm_tokens(token);
```

## API Endpoints

### Register FCM Token

```
POST /api/v1/fcm-tokens
```

Registers a new FCM token for a user.

#### Request Body

```json
{
  "sso_id": "123e4567-e89b-12d3-a456-************",
  "token": "fcm-token-string",
  "user_type": "admin",
  "user_source": "crm"
}
```

Where:
- `sso_id`: The SSO ID of the user
- `token`: The FCM token
- `user_type`: The type of user (admin, supervisor, agent)
- `user_source`: The source of the user (crm, chat, notification_service)

### Get FCM Tokens by SSO ID

```
GET /api/v1/fcm-tokens/sso/{sso_id}
```

Returns all FCM tokens for a specific SSO ID.

### Delete FCM Token

```
DELETE /api/v1/fcm-tokens/{id}
```

Deletes an FCM token by ID.

### Send Push Notification

```
POST /api/v1/push-notifications
```

Sends a push notification to multiple users.

#### Request Body

```json
{
  "sso_ids": [
    "123e4567-e89b-12d3-a456-************",
    "123e4567-e89b-12d3-a456-426614174001"
  ],
  "data": {
    "key1": "value1",
    "key2": "value2"
  },
  "notification": {
    "title": "Notification Title",
    "body": "Notification Body"
  },
  "click_action": "https://example.com/path"
}
```

Where:
- `sso_ids`: Array of SSO IDs to send the notification to
- `data`: Additional data to send with the notification (optional)
- `notification`: The notification content (required)
- `click_action`: The action to perform when the notification is clicked (optional)

## Implementation Details

The FCM integration consists of the following components:

1. **FCM Token Model**: Represents an FCM token in the system
2. **FCM Token Repository**: Handles database operations for FCM tokens
3. **FCM Token Service**: Implements business logic for FCM token operations
4. **FCM Service**: Handles sending notifications to FCM
5. **Push Notification Service**: Orchestrates sending push notifications to users
6. **FCM Token Handler**: Implements API endpoints for FCM token operations
7. **Push Notification Handler**: Implements API endpoints for sending push notifications

When sending a notification, the system:
1. Retrieves FCM tokens for the specified SSO IDs
2. Groups tokens by user source (CRM, Chat, etc.)
3. Sends notifications to each group using the appropriate FCM credentials
