# auto generated by mkrctl commit id 69e4b9bdcfc5217337eb3d359f6d835dbbfaf555
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: notif-service-cd
  namespace: argocd
spec:
  project: qontak
  source:
    repoURL: *****************:terbang-ventures/notification-service.git
    path: deploy-alicloud/cd
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd
  syncPolicy:
    automated:
      selfHeal: true
    syncOptions:
      - Validate=false
      - CreateNamespace=true
      - PrunePropagationPolicy=background
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
