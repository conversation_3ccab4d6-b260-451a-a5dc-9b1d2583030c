apiVersion: v1
kind: Service
metadata:
  name: {{ include "notif-service-chart.fullname" . }}
  labels:
    {{- include "notif-service-chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "notif-service-chart.selectorLabels" . | nindent 4 }}
---
{{- if eq .Values.applicationType "rollout" }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "notif-service-chart.fullname" . }}-canary
  labels:
    {{- include "notif-service-chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "notif-service-chart.selectorLabels" . | nindent 4 }}
{{- end }}