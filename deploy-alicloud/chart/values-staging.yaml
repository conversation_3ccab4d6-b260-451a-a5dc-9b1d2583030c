# auto generated by mkrctl commit id 69e4b9bdcfc5217337eb3d359f6d835dbbfaf555
environment: staging

# applicationType: deployment
applicationType: rollout
autoscaling:
  enabled: true
  maxReplicas: 4
  minReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
dnsConfig:
  options:
    - name: ndots
      value: "2"
fullnameOverride: ""
image:
  pullPolicy: IfNotPresent
  repository: harbor.qontak.net/mekari-hub/mekariengineering/notif-service
  tag: master
secretName: "notif-service-staging-notif-service-chart"

# ReplicaSet history limit
revisionHistoryLimit: 3

ingress:
  enabled: true
  controller:
    annotations:
      nginx.ingress.kubernetes.io/proxy-body-size: 100m
    className: nginx
    hosts:
      - host: notif-service.qontak.net
        paths:
          - path: /
            pathType: Prefix
    tls: []
nameOverride: ""
nodeSelector: {}
podAnnotations: {}
podSecurityContext: {}
ports:
  - containerPort: 4000
    name: http
    protocol: TCP
readinessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
livenessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
replicaCount: 1
resources:
  limits:
    cpu: 250m
    memory: 875Mi
  requests:
    cpu: 200m
    memory: 700Mi
securityContext: {}
service:
  port: 80
  type: ClusterIP
serviceAccount:
  create: true
  annotations: {}
  name: "qontak-notif-service-staging-service-account"
tolerations: []
datadog:
  enabled: true
  host: datadog-qontak-staging-beta.datadog
deploymentStrategy:
  nginx:
    canary:
      canaryService: notif-service-staging-notif-service-chart-canary
      stableService: notif-service-staging-notif-service-chart
      trafficRouting:
        nginx:
          stableIngress: notif-service-staging-notif-service-chart
      steps:
        - setWeight: 30
        - pause: { duration: 60s }
        - setWeight: 100
# Disabled first until successfuly migrated into Kubernetes
migrationJob:
  enabled: true
  resources:
    limits:
      cpu: 250m
      memory: 875Mi
    requests:
      cpu: 200m
      memory: 700Mi
  commandArgs: "bundle exec rake db:migrate:log && bundle exec rake db:migrate:primary"
  nodeSelector: {}
  affinity: {}
  tolerations: []

pdb:
  enabled: false

affinity:
  nodeAffinity:
    # Ensure pods are scheduled on nodes labeled as 'shared'
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node
          operator: In
          values:
          - shared

configmap:
  data:
    APP_ENV: "staging"
