# auto generated by mkrctl commit id 69e4b9bdcfc5217337eb3d359f6d835dbbfaf555
environment: production

affinity: {}
  # podAntiAffinity:
  #   preferredDuringSchedulingIgnoredDuringExecution:
  #   - weight: 100
  #     podAffinityTerm:
  #       labelSelector:
  #         matchExpressions:
  #         - key: app.kubernetes.io/instance
  #           operator: In
  #           values:
  #           - notif-service-production
  #       topologyKey: topology.kubernetes.io/hostname
# applicationType: deployment
applicationType: rollout
nodeSelector:
  node: shared-spot
autoscaling:
  enabled: true
  maxReplicas: 6
  minReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
dnsConfig:
  options:
    - name: ndots
      value: "2"
fullnameOverride: "notif-service-production-canary-alicloud"
image:
  pullPolicy: IfNotPresent
  repository: harbor.qontak.com/mekari-hub/mekariengineering/notif-service
  tag: master
secretName: "notif-service-production-canary-alicloud-notif-service-chart"

# ReplicaSet history limit
revisionHistoryLimit: 5

ingress:
  ambassador:
    enabled: false
    controller:
      annotations:
        kubernetes.io/ingress.class: ambassador
      className: ""
      hosts:
        - host: moderator.qontak.com
          paths:
            - path: /
              pathType: ImplementationSpecific
      tls: []
  nginx:
    enabled: true
    controller:
      annotations:
        nginx.ingress.kubernetes.io/proxy-body-size: 100m
      className: nginx
      hosts:
        - host: moderator.qontak.com
          paths:
            - path: /
              pathType: Prefix
      tls: []
pathMapping:
  privateInternal:
    enabled: true
    hostnames:
      - hostname: moderator.qontak.com
        label: private-mod
nameOverride: ""
podAnnotations: {}
podSecurityContext: {}
ports:
  - containerPort: 3000
    name: http
    protocol: TCP
readinessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
livenessProbe:
  httpGet:
    path: /api/v1/health
    port: http
  initialDelaySeconds: 15
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3
replicaCount: 1
resources:
  limits:
    cpu: 250m
    memory: 875Mi
  requests:
    cpu: 200m
    memory: 700Mi
securityContext:
  allowPrivilegeEscalation: false  # Prevents privilege escalation
  readOnlyRootFilesystem: false  # Ensures the root filesystem is read-only
  capabilities:
    drop:
      - ALL  # Drops all unnecessary capabilities
  seccompProfile:
    type: RuntimeDefault
service:
  port: 80
  type: ClusterIP
serviceAccount:
  create: true
  annotations: {}
  name: "qontak-moderator-production-service-account"
tolerations: []
datadog:
  enabled: true
  host: datadog-apm-qontak-production-beta.datadog
deploymentStrategy:
  name: nginx
  ambassador:
    canary:
      stableService: notif-service-production-canary-alicloud
      canaryService: notif-service-production-canary-alicloud-canary
      trafficRouting:
        ambassador:
          mappings:
            - notif-service-production-canary-alicloud-private-mod
      steps:
        - setWeight: 30
        - pause: {}
        - setWeight: 100
  nginx:
    canary:
      canaryService: notif-service-production-canary-alicloud-canary
      stableService: notif-service-production-canary-alicloud
      trafficRouting:
        nginx:
          stableIngress: notif-service-production-canary-alicloud-nginx
      steps:
        - setWeight: 30
        - pause: {}
        - setWeight: 100
# Disabled first until successfuly migrated into Kubernetes
migrationJob:
  enabled: true
  resources:
    limits:
      cpu: 250m
      memory: 875Mi
    requests:
      cpu: 200m
      memory: 700Mi
  commandArgs: "bundle exec rake db:migrate:log && bundle exec rake db:migrate:primary"
  nodeSelector: {}
  affinity: {}
  tolerations: []

sidekiq:
  enabled: true
  nodeSelector: {}
  datadog:
    enabled: true
  autoscaling:
    enabled: false
    replica: 1
    minReplicas: 1
    maxReplicas: 1
    targetCPUUtilizationPercentage: 90
    targetMemoryUtilizationPercentage: 80
  resources:
    limits:
      cpu: 250m
      memory: 438Mi
    requests:
      cpu: 200m
      memory: 350Mi

pdb:
  enabled: true
  spec:
    maxUnavailable: 25%

configmap:
  data:
    RAILS_ENV: "production"
    RAILS_MAX_THREADS: "8"
    RAILS_MIN_THREADS: "4"
    RAILS_SERVE_STATIC_FILES: "true"
    RAILS_LOG_TO_STDOUT: "true"
    BILLING_MEKARI_SERVICE_BASE_URL: "https://billing.mekari.com"
    CHAT_PANEL_SERVICE_BASE_URL: "https://internal-chat-service.qontak.com"
    CRM_SERVICE_BASE_URL: "https://internal-app.qontak.com"
    SSO_OAUTH2_URL: "https://internal-account.mekari.com/auth/oauth2/token"
    SSO_API_URL: "https://internal-api.mekari.com"
    SSO_BASE_URL: "https://internal-account.mekari.com"
    CHATBOT_BASE_URL: "https://api-chatbot.qontak.com"
    DATADOG_ENABLED: "true"
    GCHAT_BASE_URL: "https://chat.googleapis.com/v1/spaces"
    METABASE_URL: "https://metabase.qontak.com"
    HARMONIA_BASE_URL: "https://api-harmonia.mekari.com"
    METABASE_CHECK_CID_AND_WABA_ID: "221"
    METABASE_CHECK_BILLING_VERSION: "239"
    HTTP_REQUEST_TIMEOUT: "10"
    REDIS_AUTH_CHAT_EXPIRES_IN: "3600"
    DATADOG_SAMPLE_RATE_RAILS: "1.0"
    DATADOG_SAMPLE_RATE_DB_PRIMARY: "1.0"
    DATADOG_SAMPLE_RATE_DB_LOG: "1.0"
    DATADOG_SAMPLE_RATE_SIDEKIQ: "1.0"
    DATADOG_SAMPLE_RATE_REDIS: "1.0"
    REPORTING_SERVICE_BASE_URL: "https://report.qontak.com"
    LAUNCHPAD_BASE_URL: "https://api-launchpad.qontak.com"
    LAUNCHPAD_FE_URL: "https://launchpad.qontak.com"
    EMAIL_PROVIDER: "sendgrid"
    SENDGRID_MAIL_DOMAIN: "moderator.qontak.com"
