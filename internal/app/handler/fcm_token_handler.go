package handler

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// FCMTokenHandler defines the interface for FCM token handler operations
type FCMTokenHandler interface {
	RegisterToken(w http.ResponseWriter, r *http.Request)
	GetTokensBySSO(w http.ResponseWriter, r *http.Request)
	DeleteToken(w http.ResponseWriter, r *http.Request)
}

type fcmTokenHandler struct {
	fcmTokenService service.FCMTokenService
}

// NewFCMTokenHandler creates a new FCM token handler
func NewFCMTokenHandler(fcmTokenService service.FCMTokenService) FCMTokenHandler {
	return &fcmTokenHandler{
		fcmTokenService: fcmTokenService,
	}
}

// RegisterTokenRequest represents the request body for registering an FCM token
type RegisterTokenRequest struct {
	SSOID      uuid.UUID         `json:"sso_id" validate:"required"`
	Token      string            `json:"token" validate:"required"`
	UserType   model.UserType    `json:"user_type" validate:"required"`
	UserSource model.UserSource  `json:"user_source" validate:"required"`
}

// RegisterToken handles the registration of a new FCM token
func (h *fcmTokenHandler) RegisterToken(w http.ResponseWriter, r *http.Request) {
	var req RegisterTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	// Validate required fields
	if req.SSOID == uuid.Nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO ID is required"}})
		return
	}
	if req.Token == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "Token is required"}})
		return
	}
	if req.UserType == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "User type is required"}})
		return
	}
	if req.UserSource == "" {
		httpLib.BadRequest(w, []map[string]string{{"error": "User source is required"}})
		return
	}

	token := &model.FCMToken{
		SSOID:      req.SSOID,
		Token:      req.Token,
		UserType:   req.UserType,
		UserSource: req.UserSource,
	}

	if err := h.fcmTokenService.RegisterToken(r.Context(), token); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, token, nil)
}

// GetTokensBySSO handles retrieving FCM tokens by SSO ID
func (h *fcmTokenHandler) GetTokensBySSO(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "sso_id")
	ssoID, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid SSO ID"}})
		return
	}

	tokens, err := h.fcmTokenService.GetTokensBySSO(r.Context(), ssoID)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, tokens, nil)
}

// DeleteToken handles deleting an FCM token
func (h *fcmTokenHandler) DeleteToken(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid token ID"}})
		return
	}

	if err := h.fcmTokenService.DeleteToken(r.Context(), id); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "Token deleted successfully")
}
