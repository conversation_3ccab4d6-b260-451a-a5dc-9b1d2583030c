package handler

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"github.com/google/uuid"
)

// PushNotificationHandler defines the interface for push notification handler operations
type PushNotificationHandler interface {
	SendPushNotification(w http.ResponseWriter, r *http.Request)
}

type pushNotificationHandler struct {
	pushNotificationService service.PushNotificationService
}

// NewPushNotificationHandler creates a new push notification handler
func NewPushNotificationHandler(pushNotificationService service.PushNotificationService) PushNotificationHandler {
	return &pushNotificationHandler{
		pushNotificationService: pushNotificationService,
	}
}

// SendPushNotificationRequest represents the request body for sending a push notification
type SendPushNotificationRequest struct {
	SSOIDs      []uuid.UUID             `json:"sso_ids" validate:"required"`
	Data        map[string]interface{}  `json:"data"`
	Notification map[string]string      `json:"notification" validate:"required"`
	ClickAction string                  `json:"click_action"`
}

// SendPushNotification handles sending a push notification
func (h *pushNotificationHandler) SendPushNotification(w http.ResponseWriter, r *http.Request) {
	var req SendPushNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	// Validate required fields
	if len(req.SSOIDs) == 0 {
		httpLib.BadRequest(w, []map[string]string{{"error": "SSO IDs are required"}})
		return
	}
	if len(req.Notification) == 0 {
		httpLib.BadRequest(w, []map[string]string{{"error": "Notification is required"}})
		return
	}

	// Initialize data if not provided
	if req.Data == nil {
		req.Data = make(map[string]interface{})
	}

	// Send push notification
	err := h.pushNotificationService.SendPushNotification(r.Context(), req.SSOIDs, req.Data, req.Notification, req.ClickAction)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "Push notification sent successfully")
}
