package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
	CreateNotification(w http.ResponseWriter, r *http.Request)
	GetNotificationByID(w http.ResponseWriter, r *http.Request)
	GetNotifications(w http.ResponseWriter, r *http.Request)
}

type notificationHandler struct {
	notificationService service.NotificationService
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService service.NotificationService) NotificationHandler {
	return &notificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotificationRequest represents the request body for creating a notification
type CreateNotificationRequest struct {
	UserID          uuid.UUID          `json:"user_id" validate:"required"`
	Description     string             `json:"description" validate:"required"`
	ClickAction     model.ClickAction  `json:"click_action" validate:"required"`
	ClickActionURL  string             `json:"click_action_url"`
	IsReminder      bool               `json:"is_reminder"`
	NotifTypeID     uuid.UUID          `json:"notif_type_id"`
	NotifCategoryID uuid.UUID          `json:"notif_category_id"`
}

// CreateNotification handles the creation of a new notification
func (h *notificationHandler) CreateNotification(w http.ResponseWriter, r *http.Request) {
	var req CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	notification := &model.Notification{
		UserID:          req.UserID,
		Description:     req.Description,
		ClickAction:     req.ClickAction,
		ClickActionURL:  req.ClickActionURL,
		IsReminder:      req.IsReminder,
		NotifTypeID:     req.NotifTypeID,
		NotifCategoryID: req.NotifCategoryID,
	}

	if err := h.notificationService.CreateNotification(r.Context(), notification); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, notification, nil)
}

// GetNotificationByID handles retrieving a notification by ID
func (h *notificationHandler) GetNotificationByID(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
		return
	}

	notification, err := h.notificationService.GetNotificationByID(r.Context(), id)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notification, nil)
}

// GetNotifications handles retrieving all notifications with pagination
func (h *notificationHandler) GetNotifications(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	notifications, total, err := h.notificationService.GetNotifications(r.Context(), page, limit)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	// Create pagination metadata
	meta := map[string]interface{}{
		"page":       page,
		"limit":      limit,
		"total":      total,
		"total_page": (total + int64(limit) - 1) / int64(limit),
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notifications, meta)
}
