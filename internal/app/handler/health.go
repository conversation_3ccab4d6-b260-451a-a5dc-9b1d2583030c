package handler

import (
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"net/http"
)

type IHealthHandler interface {
	HealthCheck(w http.ResponseWriter, r *http.Request)
}

type healthHandler struct{}

func NewHealthHandler() IHealthHandler {
	return &healthHandler{}
}

func (h *healthHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	httpLib.WriteSuccessResponseMessage(w, http.StatusOK, "OK")
}
