package service

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationService defines the interface for notification service operations
type NotificationService interface {
	CreateNotification(ctx context.Context, notification *model.Notification) error
	GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.Notification, error)
	GetNotifications(ctx context.Context, page, limit int) ([]model.Notification, int64, error)
}

type notificationService struct {
	notificationRepo repository.NotificationRepository
}

// NewNotificationService creates a new notification service
func NewNotificationService(notificationRepo repository.NotificationRepository) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
	}
}

// CreateNotification creates a new notification
func (s *notificationService) CreateNotification(ctx context.Context, notification *model.Notification) error {
	return s.notificationRepo.Create(ctx, notification)
}

// GetNotificationByID gets a notification by ID
func (s *notificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*model.Notification, error) {
	notification, err := s.notificationRepo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, httpLib.NewRecordNotFoundException("Notification")
		}
		return nil, err
	}
	return notification, nil
}

// GetNotifications gets all notifications with pagination
func (s *notificationService) GetNotifications(ctx context.Context, page, limit int) ([]model.Notification, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	return s.notificationRepo.FindAll(ctx, page, limit)
}
