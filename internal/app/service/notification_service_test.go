package service

import (
	"context"
	"testing"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	httpLib "bitbucket.org/terbang-ventures/notification-service/internal/pkg/http"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// MockNotificationRepository is a mock implementation of repository.NotificationRepository
type MockNotificationRepository struct {
	mock.Mock
}

// Create mocks the Create method
func (m *MockNotificationRepository) Create(ctx context.Context, notification *model.Notification) error {
	args := m.Called(ctx, notification)
	return args.Error(0)
}

// FindByID mocks the FindByID method
func (m *MockNotificationRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Notification), args.Error(1)
}

// FindAll mocks the FindAll method
func (m *MockNotificationRepository) FindAll(ctx context.Context, page, limit int) ([]model.Notification, int64, error) {
	args := m.Called(ctx, page, limit)
	return args.Get(0).([]model.Notification), args.Get(1).(int64), args.Error(2)
}

// NotificationServiceTestSuite is a test suite for NotificationService
type NotificationServiceTestSuite struct {
	suite.Suite
	mockRepo     *MockNotificationRepository
	service      NotificationService
	notification *model.Notification
	ctx          context.Context
}

// SetupTest sets up the test suite
func (suite *NotificationServiceTestSuite) SetupTest() {
	suite.mockRepo = new(MockNotificationRepository)
	suite.service = NewNotificationService(suite.mockRepo)
	suite.notification = &model.Notification{
		ID:             uuid.New(),
		UserID:         uuid.New(),
		Description:    "Test notification",
		ClickAction:    model.ClickActionOpenURL,
		ClickActionURL: "https://example.com",
		IsReminder:     false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	suite.ctx = context.Background()
}

// TestCreateNotification tests the CreateNotification method
func (suite *NotificationServiceTestSuite) TestCreateNotification() {
	// Setup
	suite.mockRepo.On("Create", suite.ctx, suite.notification).Return(nil)

	// Execute
	err := suite.service.CreateNotification(suite.ctx, suite.notification)

	// Assert
	assert.NoError(suite.T(), err)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestCreateNotification_Error tests the CreateNotification method with an error
func (suite *NotificationServiceTestSuite) TestCreateNotification_Error() {
	// Setup
	expectedError := gorm.ErrInvalidData
	suite.mockRepo.On("Create", suite.ctx, suite.notification).Return(expectedError)

	// Execute
	err := suite.service.CreateNotification(suite.ctx, suite.notification)

	// Assert
	assert.Equal(suite.T(), expectedError, err)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID tests the GetNotificationByID method
func (suite *NotificationServiceTestSuite) TestGetNotificationByID() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(suite.notification, nil)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), suite.notification, notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID_NotFound tests the GetNotificationByID method with a not found error
func (suite *NotificationServiceTestSuite) TestGetNotificationByID_NotFound() {
	// Setup
	id := uuid.New()
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(nil, gorm.ErrRecordNotFound)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.Error(suite.T(), err)
	assert.IsType(suite.T(), &httpLib.RecordNotFoundException{}, err)
	assert.Nil(suite.T(), notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotificationByID_Error tests the GetNotificationByID method with an error
func (suite *NotificationServiceTestSuite) TestGetNotificationByID_Error() {
	// Setup
	id := uuid.New()
	expectedError := gorm.ErrInvalidDB
	suite.mockRepo.On("FindByID", suite.ctx, id).Return(nil, expectedError)

	// Execute
	notification, err := suite.service.GetNotificationByID(suite.ctx, id)

	// Assert
	assert.Equal(suite.T(), expectedError, err)
	assert.Nil(suite.T(), notification)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications tests the GetNotifications method
func (suite *NotificationServiceTestSuite) TestGetNotifications() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, page, limit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notifications, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidPage tests the GetNotifications method with an invalid page
func (suite *NotificationServiceTestSuite) TestGetNotifications_InvalidPage() {
	// Setup
	page := 0
	limit := 10
	expectedPage := 1
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, expectedPage, limit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notifications, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_InvalidLimit tests the GetNotifications method with an invalid limit
func (suite *NotificationServiceTestSuite) TestGetNotifications_InvalidLimit() {
	// Setup
	page := 1
	limit := 0
	expectedLimit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, page, expectedLimit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notifications, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_LargeLimit tests the GetNotifications method with a large limit
func (suite *NotificationServiceTestSuite) TestGetNotifications_LargeLimit() {
	// Setup
	page := 1
	limit := 200
	expectedLimit := 10
	var total int64 = 20
	notifications := []model.Notification{*suite.notification}
	suite.mockRepo.On("FindAll", suite.ctx, page, expectedLimit).Return(notifications, total, nil)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), notifications, result)
	assert.Equal(suite.T(), total, count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestGetNotifications_Error tests the GetNotifications method with an error
func (suite *NotificationServiceTestSuite) TestGetNotifications_Error() {
	// Setup
	page := 1
	limit := 10
	var total int64 = 0
	expectedError := gorm.ErrInvalidDB
	var notifications []model.Notification
	suite.mockRepo.On("FindAll", suite.ctx, page, limit).Return(notifications, total, expectedError)

	// Execute
	result, count, err := suite.service.GetNotifications(suite.ctx, page, limit)

	// Assert
	assert.Equal(suite.T(), expectedError, err)
	assert.Empty(suite.T(), result)
	assert.Equal(suite.T(), int64(0), count)
	suite.mockRepo.AssertExpectations(suite.T())
}

// TestNotificationServiceSuite runs the test suite
func TestNotificationServiceSuite(t *testing.T) {
	suite.Run(t, new(NotificationServiceTestSuite))
}
