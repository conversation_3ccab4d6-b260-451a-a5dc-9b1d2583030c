package service

import (
	"context"
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"bitbucket.org/terbang-ventures/notification-service/internal/pkg/fcm"
	"github.com/google/uuid"
)

// PushNotificationService defines the interface for push notification service operations
type PushNotificationService interface {
	SendPushNotification(ctx context.Context, ssoIDs []uuid.UUID, data map[string]interface{}, notification map[string]string, clickAction string) error
}

type pushNotificationService struct {
	fcmTokenRepo repository.FCMTokenRepository
	fcmService   fcm.FCMService
}

// NewPushNotificationService creates a new push notification service
func NewPushNotificationService(fcmTokenRepo repository.FCMTokenRepository, fcmService fcm.FCMService) PushNotificationService {
	return &pushNotificationService{
		fcmTokenRepo: fcmTokenRepo,
		fcmService:   fcmService,
	}
}

// SendPushNotification sends a push notification to the specified SSO IDs
func (s *pushNotificationService) SendPushNotification(ctx context.Context, ssoIDs []uuid.UUID, data map[string]interface{}, notification map[string]string, clickAction string) error {
	if len(ssoIDs) == 0 {
		return nil
	}

	// Get FCM tokens for the specified SSO IDs
	tokens, err := s.fcmTokenRepo.FindBySSOs(ctx, ssoIDs)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting FCM tokens",
			slog.Any("error", err),
			slog.Any("sso_ids", ssoIDs),
		)
		return err
	}

	if len(tokens) == 0 {
		slog.InfoContext(ctx, "No FCM tokens found for the specified SSO IDs",
			slog.Any("sso_ids", ssoIDs),
		)
		return nil
	}

	// Group tokens by user source
	tokensBySource := make(map[model.UserSource][]string)
	for _, token := range tokens {
		tokensBySource[token.UserSource] = append(tokensBySource[token.UserSource], token.Token)
	}

	// Send notifications for each user source
	for source, sourceTokens := range tokensBySource {
		err := s.fcmService.SendNotification(ctx, sourceTokens, data, notification, clickAction, source)
		if err != nil {
			slog.ErrorContext(ctx, "Error sending FCM notification",
				slog.Any("error", err),
				slog.String("source", string(source)),
				slog.Int("token_count", len(sourceTokens)),
			)
			// Continue with other sources even if one fails
			continue
		}
	}

	return nil
}
