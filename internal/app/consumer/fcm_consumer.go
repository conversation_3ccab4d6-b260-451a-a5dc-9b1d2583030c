package consumer

import (
	"context"
	"encoding/json"
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"github.com/IBM/sarama"
	"github.com/google/uuid"
)

// FCMNotificationMessage represents the message structure for FCM notifications
type FCMNotificationMessage struct {
	SSOIDs       []uuid.UUID            `json:"sso_ids"`
	Data         map[string]interface{} `json:"data"`
	Notification map[string]string      `json:"notification"`
	ClickAction  string                 `json:"click_action"`
}

// FCMConsumer is a Kafka consumer for FCM notifications
type FCMConsumer struct {
	pushNotificationService service.PushNotificationService
}

// NewFCMConsumer creates a new FCM consumer
func NewFCMConsumer(pushNotificationService service.PushNotificationService) *FCMConsumer {
	return &FCMConsumer{
		pushNotificationService: pushNotificationService,
	}
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (c *FCMConsumer) Setup(session sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (c *FCMConsumer) Cleanup(session sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim is called for each consumer session to consume messages
func (c *FCMConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				slog.Info("message channel was closed")
				return nil
			}

			slog.InfoContext(context.Background(), "FCM notification message received",
				slog.String("value", string(message.Value)),
				slog.Any("timestamp", message.Timestamp),
				slog.String("topic", message.Topic),
			)

			// Process the message
			if err := c.processMessage(session.Context(), message.Value); err != nil {
				slog.ErrorContext(session.Context(), "Error processing FCM notification message",
					slog.Any("error", err),
					slog.String("value", string(message.Value)),
				)
				// Continue processing other messages even if one fails
			}

			// Mark the message as processed
			session.MarkMessage(message, "")
		case <-session.Context().Done():
			return nil
		}
	}
}

// processMessage processes an FCM notification message
func (c *FCMConsumer) processMessage(ctx context.Context, messageValue []byte) error {
	var message FCMNotificationMessage
	if err := json.Unmarshal(messageValue, &message); err != nil {
		slog.ErrorContext(ctx, "Error unmarshaling FCM notification message",
			slog.Any("error", err),
			slog.String("value", string(messageValue)),
		)
		return err
	}

	// Validate the message
	if len(message.SSOIDs) == 0 {
		slog.WarnContext(ctx, "FCM notification message has no SSO IDs",
			slog.String("value", string(messageValue)),
		)
		return nil
	}

	if len(message.Notification) == 0 {
		slog.WarnContext(ctx, "FCM notification message has no notification content",
			slog.String("value", string(messageValue)),
		)
		return nil
	}

	// Initialize data if not provided
	if message.Data == nil {
		message.Data = make(map[string]interface{})
	}

	// Send the push notification
	err := c.pushNotificationService.SendPushNotification(
		ctx,
		message.SSOIDs,
		message.Data,
		message.Notification,
		message.ClickAction,
	)
	if err != nil {
		slog.ErrorContext(ctx, "Error sending push notification",
			slog.Any("error", err),
			slog.Any("sso_ids", message.SSOIDs),
		)
		return err
	}

	slog.InfoContext(ctx, "Push notification sent successfully",
		slog.Any("sso_ids", message.SSOIDs),
	)
	return nil
}
