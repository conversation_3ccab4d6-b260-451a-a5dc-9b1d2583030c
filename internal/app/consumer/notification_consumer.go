package consumer

import (
	"github.com/IBM/sarama"
)

// NotificationConsumer handles notification messages from Kafka
type NotificationConsumer struct {
	// Add dependencies here
}

// NewNotificationConsumer creates a new notification consumer
func NewNotificationConsumer() *NotificationConsumer {
	return &NotificationConsumer{}
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (c *NotificationConsumer) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (c *NotificationConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages()
func (c *NotificationConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		// slog.InfoContext(session.Context(), "Received notification message",
		// 	slog.String("topic", message.Topic),
		// 	slog.Int("partition", message.Partition),
		// 	slog.Int("offset", message.Offset),
		// )

		// Process the message here

		session.MarkMessage(message, "")
	}
	return nil
}
