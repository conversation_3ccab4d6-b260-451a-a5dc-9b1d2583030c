package repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FCMTokenRepository defines the interface for FCM token repository operations
type FCMTokenRepository interface {
	Create(ctx context.Context, token *model.FCMToken) error
	FindByID(ctx context.Context, id uuid.UUID) (*model.FCMToken, error)
	FindBySSO(ctx context.Context, ssoID uuid.UUID) ([]model.FCMToken, error)
	FindByToken(ctx context.Context, token string) (*model.FCMToken, error)
	FindBySSOs(ctx context.Context, ssoIDs []uuid.UUID) ([]model.FCMToken, error)
	Update(ctx context.Context, token *model.FCMToken) error
	Delete(ctx context.Context, id uuid.UUID) error
}

type fcmTokenRepository struct {
	db *gorm.DB
}

// NewFCMTokenRepository creates a new FCM token repository
func NewFCMTokenRepository(db *gorm.DB) FCMTokenRepository {
	return &fcmTokenRepository{
		db: db,
	}
}

// <PERSON>reate creates a new FCM token
func (r *fcmTokenRepository) Create(ctx context.Context, token *model.FCMToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

// FindByID finds an FCM token by ID
func (r *fcmTokenRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.FCMToken, error) {
	var token model.FCMToken
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&token).Error
	if err != nil {
		// Pass through the error as is, including gorm.ErrRecordNotFound
		return nil, err
	}
	return &token, nil
}

// FindBySSO finds FCM tokens by SSO ID
func (r *fcmTokenRepository) FindBySSO(ctx context.Context, ssoID uuid.UUID) ([]model.FCMToken, error) {
	var tokens []model.FCMToken
	err := r.db.WithContext(ctx).Where("sso_id = ?", ssoID).Find(&tokens).Error
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// FindByToken finds an FCM token by token string
func (r *fcmTokenRepository) FindByToken(ctx context.Context, token string) (*model.FCMToken, error) {
	var fcmToken model.FCMToken
	err := r.db.WithContext(ctx).Where("token = ?", token).First(&fcmToken).Error
	if err != nil {
		// Pass through the error as is, including gorm.ErrRecordNotFound
		return nil, err
	}
	return &fcmToken, nil
}

// FindBySSOs finds FCM tokens by multiple SSO IDs
func (r *fcmTokenRepository) FindBySSOs(ctx context.Context, ssoIDs []uuid.UUID) ([]model.FCMToken, error) {
	var tokens []model.FCMToken
	err := r.db.WithContext(ctx).Where("sso_id IN ?", ssoIDs).Find(&tokens).Error
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// Update updates an FCM token
func (r *fcmTokenRepository) Update(ctx context.Context, token *model.FCMToken) error {
	return r.db.WithContext(ctx).Save(token).Error
}

// Delete deletes an FCM token
func (r *fcmTokenRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.FCMToken{}, id).Error
}
