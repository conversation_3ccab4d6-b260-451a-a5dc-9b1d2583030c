package server

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-chi/chi/v5"
	stdLog "log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

type APIServer struct {
	address    string
	appContext *AppContext
	httpServer *http.Server
}

func NewAPIServer(address string, appContext *AppContext) *APIServer {
	app := chi.NewRouter()

	r := NewRouter(appContext)
	r.RegisterRoutes(app)

	server := &http.Server{
		Addr:              address,
		Handler:           app,
		ReadHeaderTimeout: 1 * time.Minute,
	}

	return &APIServer{
		address:    address,
		appContext: appContext,
		httpServer: server,
	}
}

func (s *APIServer) Start(ctx context.Context) {
	quit := make(chan os.Signal)

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			stdLog.Fatalf("failed to start the server: %v", err)
		}
	}()

	slog.Info(fmt.Sprintf("Server listed on %s", s.address))

	s.gracefulShutdown(ctx, quit)
}

func (s *APIServer) gracefulShutdown(ctx context.Context, quit chan os.Signal) {
	signal.Notify(
		quit,
		os.Interrupt,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT,
	)

	q := <-quit

	slog.WarnContext(context.Background(), "shutting down server ...",
		slog.Any("signal", q),
	)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	s.httpServer.SetKeepAlivesEnabled(false)
	if err := s.httpServer.Shutdown(ctx); err != nil {
		slog.ErrorContext(context.Background(), "error while shutting down server",
			slog.Any("error", err),
		)
	}

	slog.Info("server exiting")
}
