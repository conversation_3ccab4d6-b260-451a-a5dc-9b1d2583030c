package server

import (
	"github.com/go-chi/chi/v5"
)

type Router struct {
	AppContext *AppContext
}

func NewRouter(ctx *AppContext) *Router {
	return &Router{
		AppContext: ctx,
	}
}

func (r *Router) RegisterRoutes(chiRouter chi.Router) {
	chiRouter.Route("/api", func(rt chi.Router) {
		r.registerV1Route(rt)
	})
}

func (r *Router) registerV1Route(chiRouter chi.Router) {
	chiRouter.Route("/v1", func(rt chi.Router) {
		// Health routes
		rt.Route("/health", func(rt2 chi.Router) {
			rt2.Get("/", r.AppContext.HealthHandler.HealthCheck)
		})

		// Notification routes
		if r.AppContext.NotificationHandler != nil {
			rt.Route("/notifications", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.NotificationHandler.CreateNotification)
				rt2.Get("/", r.AppContext.NotificationHandler.GetNotifications)
				rt2.Get("/{id}", r.AppContext.NotificationHandler.GetNotificationByID)
			})
		}

		// FCM Token routes
		if r.AppContext.FCMTokenHandler != nil {
			rt.Route("/fcm-tokens", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.FCMTokenHandler.RegisterToken)
				rt2.Get("/sso/{sso_id}", r.AppContext.FCMTokenHandler.GetTokensBySSO)
				rt2.Delete("/{id}", r.AppContext.FCMTokenHandler.DeleteToken)
			})
		}

		// Push Notification routes
		if r.AppContext.PushNotificationHandler != nil {
			rt.Route("/push-notifications", func(rt2 chi.Router) {
				rt2.Post("/", r.AppContext.PushNotificationHandler.SendPushNotification)
			})
		}
	})
}
