package worker

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"bitbucket.org/terbang-ventures/notification-service/pkg/kafka"
	"github.com/IBM/sarama"
)

type Worker struct {
	topic         string
	consumerGroup kafka.IConsumerGroup
	consumer      sarama.ConsumerGroupHandler
}

func NewWorker(topic string, consumerGroup kafka.IConsumerGroup, consumer sarama.ConsumerGroupHandler) *Worker {
	return &Worker{
		topic:         topic,
		consumerGroup: consumerGroup,
		consumer:      consumer,
	}
}

func (w *Worker) Start(ctx context.Context) {
	quit := make(chan os.Signal, 1)
	keepRunning := true
	ctx, cancel := context.WithCancel(ctx)
	signal.Notify(
		quit,
		os.Interrupt,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT,
	)

	var wg sync.WaitGroup
	wg.Add(1)

	slog.InfoContext(context.Background(), "starting kafka consumer group",
		slog.String("topic", w.topic),
		slog.String("groupID", w.consumerGroup.GetGroupID()),
	)

	// TODO: make process parallel with multiple go routine / more threads
	go func() {
		defer wg.Done()

		for {
			if err := w.consumerGroup.Consume(ctx, []string{w.topic}, w.consumer); err != nil {
				if errors.Is(err, sarama.ErrClosedConsumerGroup) {
					return
				}
				slog.ErrorContext(context.Background(), "error while consume from kafka",
					slog.Any("error", err),
					slog.String("topic", w.topic),
				)
			}

			// Check if context was cancelled, signaling that the consumer should stop
			if ctx.Err() != nil {
				return
			}
		}
	}()

	for keepRunning {
		select {
		case <-ctx.Done():
			slog.Info("terminating: context cancelled")
			keepRunning = false
		case <-quit:
			slog.Info("terminating: via signal")
			keepRunning = false
		}
	}

	cancel()
	wg.Wait()

	slog.InfoContext(context.Background(), "shutting down kafka consumer group",
		slog.String("topic", w.topic),
		slog.String("groupID", w.consumerGroup.GetGroupID()),
	)
}
