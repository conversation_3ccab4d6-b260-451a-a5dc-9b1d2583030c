package domains

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
)

type SuccessResponse struct {
	Data interface{} `json:"data"`
	Meta interface{} `json:"meta"`
}

type ErrorResponse struct {
	Meta   interface{} `json:"meta"`
	Errors interface{} `json:"errors"`
}

func writeResponse(w http.ResponseWriter, status int, contentType string, r interface{}) {
	response, err := json.Marshal(r)
	if err != nil {
		slog.ErrorContext(context.Background(), "error while marshaling the response",
			slog.Any("error", err),
			slog.Int("status", status),
			slog.String("content_type", contentType),
			slog.Any("response", r),
		)
	}

	w.Header().Set("Content-Type", "application/json")
	w.<PERSON>rite<PERSON>eader(status)
	_, err = w.Write(response)
	if err != nil {
		slog.ErrorContext(context.Background(), "error while writing the response",
			slog.Any("error", err),
			slog.Int("status", status),
			slog.String("content_type", contentType),
			slog.Any("response", r),
		)
	}
}

func WriteSuccessResponse(w http.ResponseWriter, status int, data interface{}, meta interface{}) {
	writeResponse(w, status, "application/json", SuccessResponse{
		Data: data,
		Meta: meta,
	})
}

func WriteSuccessResponseMessage(w http.ResponseWriter, status int, message string) {
	writeResponse(w, status, "application/json", SuccessResponse{
		Data: nil,
		Meta: struct {
			Message string `json:"message"`
		}{
			Message: message,
		},
	})
}

func WriteErrorResponse(w http.ResponseWriter, status int, errors interface{}, meta interface{}) {
	writeResponse(w, status, "application/json", ErrorResponse{
		Meta:   meta,
		Errors: errors,
	})
}

func WriteErrorResponseMessage(w http.ResponseWriter, status int, errors interface{}, message string) {
	writeResponse(w, status, "application/json", ErrorResponse{
		Errors: errors,
		Meta: struct {
			Message string `json:"message"`
		}{
			Message: message,
		},
	})
}

func InternalServerError(w http.ResponseWriter, err string) {
	meta := map[string]string{"message": ErrInternalServerError.Error()}
	errors := []map[string]string{
		{"error": err},
	}

	WriteErrorResponse(w, http.StatusInternalServerError, errors, meta)
}

func RecordNotFound(w http.ResponseWriter, modelName string) {
	meta := map[string]string{"message": ErrNotFound.Error()}
	errors := []map[string]string{
		{"error": fmt.Sprintf("%s not found", modelName)},
	}

	WriteErrorResponse(w, http.StatusNotFound, errors, meta)
}

func BadRequest(w http.ResponseWriter, errors interface{}) {
	meta := map[string]string{"message": ErrBadRequest.Error()}
	WriteErrorResponse(w, http.StatusBadRequest, errors, meta)
}

func Unauthorized(w http.ResponseWriter) {
	meta := map[string]string{"message": ErrUnauthorized.Error()}
	errors := []map[string]string{
		{"error": "You are unauthorized"},
	}

	WriteErrorResponse(w, http.StatusUnauthorized, errors, meta)
}

func SessionExpired(w http.ResponseWriter) {
	meta := map[string]string{"message": ErrUnauthorized.Error()}
	errors := []map[string]string{
		{"error": "Your session has expired"},
	}

	WriteErrorResponse(w, http.StatusUnauthorized, errors, meta)
}

func UnprocessableEntityError(w http.ResponseWriter, err string) {
	meta := map[string]string{"message": ErrUnprocessableEntity.Error()}
	errors := []map[string]string{
		{"error": err},
	}

	WriteErrorResponse(w, http.StatusUnprocessableEntity, errors, meta)
}

func Error(w http.ResponseWriter, err error) {
	switch e := err.(type) {
	case *InternalErrorException:
		InternalServerError(w, e.Error())
	case *UnauthorizedException:
		Unauthorized(w)
	case *BadRequestException:
		BadRequest(w, []map[string]string{{"error": err.Error()}})
	case *RecordNotFoundException:
		RecordNotFound(w, e.ModelName)
	case *UnprocessableEntityException:
		UnprocessableEntityError(w, e.Error())
	default:
		InternalServerError(w, e.Error())
	}
}
