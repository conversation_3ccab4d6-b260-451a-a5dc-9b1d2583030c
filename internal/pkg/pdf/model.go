package pdf

// PDFContent represents the extracted content from a PDF file
type PDFContent struct {
	Text     string            `json:"text"`
	Metadata map[string]string `json:"metadata"`
	Pages    int               `json:"pages"`
	FileName string            `json:"file_name"`
}

// PDFReadOptions represents options for reading a PDF file
type PDFReadOptions struct {
	ExtractText    bool `json:"extract_text"`
	ExtractMetadata bool `json:"extract_metadata"`
	StartPage      int  `json:"start_page"`
	EndPage        int  `json:"end_page"`
}

// DefaultReadOptions returns the default options for reading a PDF
func DefaultReadOptions() PDFReadOptions {
	return PDFReadOptions{
		ExtractText:    true,
		ExtractMetadata: true,
		StartPage:      1,
		EndPage:        0, // 0 means all pages
	}
}
