package fcm

import (
	"context"
	"log/slog"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
)

// AuthMonitor provides utilities to monitor Firebase authentication status
type AuthMonitor struct {
	fcmService *fcmService
}

// NewAuthMonitor creates a new authentication monitor
func NewAuthMonitor(service FCMService) *AuthMonitor {
	if fcmSvc, ok := service.(*fcmService); ok {
		return &AuthMonitor{fcmService: fcmSvc}
	}
	return nil
}

// GetAuthStatus returns authentication status for all configured user sources
func (m *AuthMonitor) GetAuthStatus(ctx context.Context) map[string]AuthStatus {
	if m.fcmService == nil {
		return nil
	}

	sources := []model.UserSource{
		model.UserSourceCRM,
		model.UserSourceChat,
		model.UserSourceNotificationSvc,
	}

	status := make(map[string]AuthStatus)
	for _, source := range sources {
		isCached, cachedAt := m.fcmService.GetClientInfo(source)

		authStatus := AuthStatus{
			UserSource:  string(source),
			IsCached:    isCached,
			CachedAt:    cachedAt,
			IsConnected: false,
		}

		if isCached {
			// Test connection by attempting to get client (won't re-authenticate if cached)
			_, err := m.fcmService.getClient(ctx, source)
			authStatus.IsConnected = err == nil
			authStatus.LastError = err
		}

		status[string(source)] = authStatus
	}

	return status
}

// AuthStatus represents the authentication status for a user source
type AuthStatus struct {
	UserSource  string    `json:"user_source"`
	IsCached    bool      `json:"is_cached"`
	CachedAt    time.Time `json:"cached_at,omitempty"`
	IsConnected bool      `json:"is_connected"`
	LastError   error     `json:"last_error,omitempty"`
}

// LogAuthStatus logs the current authentication status
func (m *AuthMonitor) LogAuthStatus(ctx context.Context) {
	if m.fcmService == nil {
		slog.WarnContext(ctx, "Auth monitor not properly initialized")
		return
	}

	statuses := m.GetAuthStatus(ctx)

	slog.InfoContext(ctx, "Firebase FCM Authentication Status Summary")

	for source, status := range statuses {
		if status.IsCached {
			slog.InfoContext(ctx, "FCM client status",
				slog.String("user_source", source),
				slog.Bool("cached", status.IsCached),
				slog.Time("cached_since", status.CachedAt),
				slog.Bool("connected", status.IsConnected),
				slog.Duration("cache_age", time.Since(status.CachedAt)),
			)
		} else {
			slog.InfoContext(ctx, "FCM client status",
				slog.String("user_source", source),
				slog.Bool("cached", status.IsCached),
				slog.String("status", "not_initialized"),
			)
		}

		if status.LastError != nil {
			slog.ErrorContext(ctx, "FCM authentication error",
				slog.String("user_source", source),
				slog.Any("error", status.LastError),
			)
		}
	}
}
