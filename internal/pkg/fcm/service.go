package fcm

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"bitbucket.org/terbang-ventures/notification-service/config"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"google.golang.org/api/option"
)

// FCMService defines the interface for FCM operations
type FCMService interface {
	SendNotification(ctx context.Context, tokens []string, data map[string]interface{}, notification map[string]string, clickAction string, userSource model.UserSource) error
	GetClientInfo(userSource model.UserSource) (bool, time.Time)
}

// clientInfo holds information about cached clients
type clientInfo struct {
	client    *messaging.Client
	createdAt time.Time
	projectID string
}

type fcmService struct {
	clients map[string]*clientInfo // Enhanced client cache with metadata
	mutex   sync.RWMutex           // Thread-safe access
}

// NewFCMService creates a new FCM service
func NewFCMService() FCMService {
	return &fcmService{
		clients: make(map[string]*clientInfo),
	}
}

// GetClientInfo returns information about cached clients
func (s *fcmService) GetClientInfo(userSource model.UserSource) (bool, time.Time) {
	source := string(userSource)
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if info, exists := s.clients[source]; exists {
		return true, info.createdAt
	}
	return false, time.Time{}
}

// getClient gets or creates a Firebase messaging client for the given user source
// This method handles Google OAuth2 authentication and token caching automatically
func (s *fcmService) getClient(ctx context.Context, userSource model.UserSource) (*messaging.Client, error) {
	source := string(userSource)

	// Check if client is already cached (avoids re-authentication)
	s.mutex.RLock()
	if info, exists := s.clients[source]; exists {
		s.mutex.RUnlock()
		slog.DebugContext(ctx, "Using cached Firebase messaging client",
			slog.String("user_source", source),
			slog.String("project_id", info.projectID),
			slog.Time("cached_since", info.createdAt),
		)
		return info.client, nil
	}
	s.mutex.RUnlock()

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Double-check after acquiring write lock
	if info, exists := s.clients[source]; exists {
		return info.client, nil
	}

	// Get FCM configuration based on user source
	fcmConfig := config.GetFCMConfig(source)
	if fcmConfig.ProjectID == "" || fcmConfig.CredentialsFile == "" {
		return nil, fmt.Errorf("FCM configuration not found for source: %s", userSource)
	}

	slog.InfoContext(ctx, "Initializing Firebase messaging client with Google authentication",
		slog.String("user_source", source),
		slog.String("project_id", fcmConfig.ProjectID),
		slog.String("credentials_file", fcmConfig.CredentialsFile),
	)

	// Initialize Firebase app with service account credentials
	// The Firebase Admin SDK automatically handles:
	// 1. OAuth2 token acquisition using service account key
	// 2. Token caching and automatic refresh
	// 3. Retry logic for token refresh failures
	opt := option.WithCredentialsFile(fcmConfig.CredentialsFile)
	app, err := firebase.NewApp(ctx, &firebase.Config{
		ProjectID: fcmConfig.ProjectID,
	}, opt)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to initialize Firebase app",
			slog.String("user_source", source),
			slog.String("project_id", fcmConfig.ProjectID),
			slog.Any("error", err),
		)
		return nil, fmt.Errorf("error initializing Firebase app for source %s: %w", userSource, err)
	}

	// Get messaging client (this triggers the initial OAuth2 authentication)
	client, err := app.Messaging(ctx)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get Firebase messaging client",
			slog.String("user_source", source),
			slog.String("project_id", fcmConfig.ProjectID),
			slog.Any("error", err),
		)
		return nil, fmt.Errorf("error getting messaging client for source %s: %w", userSource, err)
	}

	// Cache the client with metadata for monitoring
	info := &clientInfo{
		client:    client,
		createdAt: time.Now(),
		projectID: fcmConfig.ProjectID,
	}
	s.clients[source] = info

	slog.InfoContext(ctx, "Firebase messaging client initialized and cached successfully",
		slog.String("user_source", source),
		slog.String("project_id", fcmConfig.ProjectID),
		slog.Time("cached_at", info.createdAt),
	)

	return client, nil
}

// SendNotification sends a notification to the specified tokens
func (s *fcmService) SendNotification(ctx context.Context, tokens []string, data map[string]interface{}, notification map[string]string, clickAction string, userSource model.UserSource) error {
	if len(tokens) == 0 {
		return fmt.Errorf("no tokens provided")
	}

	client, err := s.getClient(ctx, userSource)
	if err != nil {
		return err
	}

	// Convert data map to string map (FCM requirement)
	dataStrings := make(map[string]string)
	for k, v := range data {
		if str, ok := v.(string); ok {
			dataStrings[k] = str
		} else {
			// Convert non-string values to JSON
			if jsonBytes, err := json.Marshal(v); err == nil {
				dataStrings[k] = string(jsonBytes)
			} else {
				dataStrings[k] = fmt.Sprintf("%v", v)
			}
		}
	}

	var successCount, failureCount int
	var lastError error

	// Send individual messages instead of using batch/multicast
	for i, token := range tokens {
		// Create individual FCM message
		message := &messaging.Message{
			Data: dataStrings,
			Notification: &messaging.Notification{
				Title: notification["title"],
				Body:  notification["body"],
			},
			Android: &messaging.AndroidConfig{
				Notification: &messaging.AndroidNotification{
					ClickAction: clickAction,
				},
			},
			APNS: &messaging.APNSConfig{
				Payload: &messaging.APNSPayload{
					Aps: &messaging.Aps{
						Category: clickAction,
					},
				},
			},
			Webpush: &messaging.WebpushConfig{
				FCMOptions: &messaging.WebpushFCMOptions{
					Link: clickAction,
				},
			},
			Token: token,
		}

		// Send individual message
		messageID, err := client.Send(ctx, message)
		if err != nil {
			slog.WarnContext(ctx, "FCM message failed for token",
				slog.String("user_source", string(userSource)),
				slog.Int("token_index", i),
				slog.String("error", err.Error()),
			)
			failureCount++
			lastError = err
		} else {
			slog.DebugContext(ctx, "FCM message sent successfully",
				slog.String("user_source", string(userSource)),
				slog.Int("token_index", i),
				slog.String("message_id", messageID),
			)
			successCount++
		}
	}

	// Log the results
	slog.InfoContext(ctx, "FCM messages sent",
		slog.String("user_source", string(userSource)),
		slog.Int("token_count", len(tokens)),
		slog.Int("success_count", successCount),
		slog.Int("failure_count", failureCount),
	)

	// Return error only if all messages failed
	if failureCount == len(tokens) && lastError != nil {
		return fmt.Errorf("all FCM messages failed, last error: %w", lastError)
	}

	return nil
}
