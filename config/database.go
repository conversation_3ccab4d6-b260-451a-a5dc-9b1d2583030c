package config

import (
	stdLog "log"

	dbLib "bitbucket.org/mid-kelola-indonesia/go-utils/database"
	"gorm.io/gorm"
)

// InitDatabase init database connection
func InitDatabase(cfg dbLib.Config) *gorm.DB {
	gormDBConn, err := dbLib.InitGORM(dbLib.POSTGRESQL, dbLib.GORMConfig{
		Config: cfg,
	})
	if err != nil {
		stdLog.Printf("error while init gorm database connection: %v", err)
		return nil
	}

	return gormDBConn
}
