package config

import (
	"log/slog"

	"bitbucket.org/terbang-ventures/notification-service/pkg/kafka"
)

// InitKafkaSyncProducer init kafka sync producer
func InitKafkaSyncProducer(cfg kafka.Config) kafka.Producer {
	producer, err := kafka.NewSyncProducer(cfg)
	if err != nil {
		slog.Error("error while init kafka sync producer", slog.Any("error", err))
		return nil
	}

	return producer
}

// InitKafkaSyncProducerWithoutFatal init kafka sync producer
func InitKafkaSyncProducerWithoutFatal(cfg kafka.Config) (kafka.Producer, error) {
	producer, err := kafka.NewSyncProducer(cfg)
	if err != nil {
		slog.Error("error while init kafka sync producer", slog.Any("error", err))
		return nil, err
	}

	return producer, nil
}

// InitKafkaConsumerGroup init kafka consumer group
func InitKafkaConsumerGroup(cfg kafka.Config, groupID string) kafka.IConsumerGroup {
	consumerGroup, err := kafka.NewConsumerGroup(cfg, groupID)
	if err != nil {
		slog.Error("error while init kafka consumer group", slog.Any("error", err))
		return nil
	}

	return consumerGroup
}

// InitKafkaConsumerGroup init kafka consumer group
func InitKafkaConsumerGroupWithoutFatal(cfg kafka.Config, groupID string) (kafka.IConsumerGroup, error) {
	consumerGroup, err := kafka.NewConsumerGroup(cfg, groupID)
	if err != nil {
		slog.Error("error while init kafka consumer group", slog.Any("error", err))
		return nil, err
	}

	return consumerGroup, nil
}
