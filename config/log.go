package config

import (
	"bitbucket.org/mid-kelola-indonesia/go-utils/logger"
	"fmt"
	stdlog "log"
	"log/slog"
)

type LoggerConf struct {
	ServiceName        string
	ServiceEnvironment string
}

// InitAppLogger init app logger, set logger as default logger
func InitAppLogger(cfg LoggerConf) {
	log, err := logger.SlogOption{
		Resource: map[string]string{
			"service.name": cfg.ServiceName,
			"service.env":  cfg.ServiceEnvironment,
		},
	}.NewSlog()
	if err != nil {
		err = fmt.Errorf("prepare logger error: %w", err)
		stdlog.Fatal(err)
		return
	}

	slog.SetDefault(log)
}
