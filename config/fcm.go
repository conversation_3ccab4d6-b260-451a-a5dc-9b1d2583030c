package config

import (
	"github.com/spf13/viper"
)

// FCMConfig represents the configuration for Firebase Cloud Messaging
type FCMConfig struct {
	ProjectID      string
	CredentialsFile string
}

// GetFCMConfig returns the FCM configuration for a specific source
func GetFCMConfig(source string) FCMConfig {
	switch source {
	case "crm":
		return FCMConfig{
			ProjectID:      viper.GetString("FCM_CRM_PROJECT_ID"),
			CredentialsFile: viper.GetString("FCM_CRM_CREDENTIALS_FILE"),
		}
	case "chat":
		return FCMConfig{
			ProjectID:      viper.GetString("FCM_CHAT_PROJECT_ID"),
			CredentialsFile: viper.GetString("FCM_CHAT_CREDENTIALS_FILE"),
		}
	default:
		return FCMConfig{
			ProjectID:      viper.GetString("FCM_DEFAULT_PROJECT_ID"),
			CredentialsFile: viper.GetString("FCM_DEFAULT_CREDENTIALS_FILE"),
		}
	}
}
