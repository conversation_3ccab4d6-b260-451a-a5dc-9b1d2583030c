package kafka

import (
	"context"
	"github.com/IBM/sarama"
	"log/slog"
)

type IConsumerGroup interface {
	Consume(ctx context.Context, topics []string, handler sarama.ConsumerGroupHandler) error
	Close() error
	GetGroupID() string
}

type ConsumerGroup struct {
	consumerGroup sarama.ConsumerGroup
	groupID       string
	config        Config
}

func NewConsumerGroup(cfg Config, groupID string) (IConsumerGroup, error) {
	config := NewConsumerGroupConfig(cfg)

	consumerGroup, err := sarama.NewConsumerGroup(cfg.Brokers, groupID, config)
	if err != nil {
		slog.ErrorContext(context.Background(), "unable to initialized kafka consumer group",
			slog.Any("error", err),
		)
		return nil, err
	}

	return &ConsumerGroup{
		consumerGroup: consumerGroup,
		groupID:       groupID,
		config:        cfg,
	}, nil
}

func (c *ConsumerGroup) Consume(ctx context.Context, topics []string, handler sarama.ConsumerGroupHandler) error {
	topics = c.appendPrefixForTopics(topics)
	return c.consumerGroup.Consume(ctx, topics, handler)
}

func (c *ConsumerGroup) Close() error {
	return c.consumerGroup.Close()
}

func (c *ConsumerGroup) appendPrefixForTopics(topics []string) []string {
	var t []string

	for _, topic := range topics {
		t = append(t, c.config.Prefix+topic)
	}

	return t
}

func (c *ConsumerGroup) GetGroupID() string {
	return c.groupID
}
