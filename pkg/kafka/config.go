package kafka

import (
	"crypto/tls"
	"crypto/x509"
	"log/slog"
	"time"

	"github.com/IBM/sarama"
)

type Config struct {
	ClientID string
	Brokers  []string
	Sasl     bool
	Username string
	Password string
	Prefix   string
	Cert     string
}

// NewProducerConfig Initialize configuration for producer
func NewProducerConfig(cfg Config) *sarama.Config {
	config := defaultConfig(cfg)

	// Producer configuration
	config.Producer.Retry.Max = 5
	config.Producer.RequiredAcks = sarama.WaitForLocal
	config.Producer.Return.Successes = true
	config.Producer.Retry.Backoff = 100 * time.Millisecond

	return config
}

// NewConsumerGroupConfig Initialize configuration for consumer group
func NewConsumerGroupConfig(cfg Config) *sarama.Config {
	config := defaultConfig(cfg)

	// Consumer configuration
	config.Consumer.Offsets.Initial = sarama.OffsetNewest
	config.Consumer.Offsets.AutoCommit.Enable = true
	config.Consumer.Offsets.AutoCommit.Interval = 1 * time.Second

	return config
}

// Default configuration for producer and consumer
func defaultConfig(cfg Config) *sarama.Config {
	config := sarama.NewConfig()

	// General configuration
	config.ClientID = cfg.ClientID
	config.Version = sarama.V2_2_0_0
	config.Metadata.Full = true
	config.Net.KeepAlive = 30 * time.Second

	if cfg.Sasl {
		config.Net.SASL.Enable = true
		config.Net.SASL.User = cfg.Username
		config.Net.SASL.Password = cfg.Password
		config.Net.SASL.Handshake = true
		config.Net.TLS.Enable = true

		clientCertPool := x509.NewCertPool()
		ok := clientCertPool.AppendCertsFromPEM([]byte(cfg.Cert))
		if !ok {
			slog.Error("kafka sasl certificate is invalid")
		}
		config.Net.TLS.Config = &tls.Config{
			RootCAs: clientCertPool,
			/* #nosec */
			InsecureSkipVerify: true,
		}
	}

	return config
}
