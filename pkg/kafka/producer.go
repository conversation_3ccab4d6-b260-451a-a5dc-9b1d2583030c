package kafka

import (
	"context"
	"github.com/IBM/sarama"
	saramatrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/IBM/sarama.v1"
	"log/slog"
)

type Producer interface {
	Publish(topic, message string) error
	Close() error
}

type SyncProducer struct {
	syncProducer sarama.SyncProducer
	config       Config
}

func NewSyncProducer(cfg Config) (Producer, error) {
	config := NewProducerConfig(cfg)

	syncProducer, err := sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		slog.ErrorContext(context.Background(), "unable to initialized kafka producer",
			slog.Any("error", err),
		)
		return nil, err
	}
	syncProducer = saramatrace.WrapSyncProducer(config, syncProducer)

	return &SyncProducer{
		syncProducer: syncProducer,
		config:       cfg,
	}, nil
}

func (p *SyncProducer) Publish(topic, message string) error {
	topic = p.config.Prefix + topic
	partition, offset, err := p.syncProducer.SendMessage(&sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(message),
	})
	if err != nil {
		slog.ErrorContext(context.Background(), "error while publish message to kafka",
			slog.Any("error", err),
			slog.String("topic", topic),
			slog.String("message", message),
		)
		return err
	}

	slog.InfoContext(context.Background(), "message published to kafka",
		slog.String("topic", topic),
		slog.Int64("partition", int64(partition)),
		slog.String("message", message),
		slog.Int64("offset", offset),
	)

	return nil
}

func (p *SyncProducer) Close() error {
	return p.syncProducer.Close()
}
