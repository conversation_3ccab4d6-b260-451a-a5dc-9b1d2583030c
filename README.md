![technology:go](https://img.shields.io/badge/technology-go-blue.svg)
# Broadcast Service

A service for Broadcasting system

## How do I get set up? ###

1. Install go 1.23.2 or later
2. Prepare dependencies on your local
3. Copy .env.example to .env and then set the value as needed especially for dependencies.
    * Or you can set up vault on your local, and follow the keys from .env.example and set the VAULT_ENABLED = true
4. How to run server API:
    * `make build && ./bin/notification-service server`
    * or alternatively `make run ARGS=server`
    * see more command with `make build && ./bin/notification-service server --help`
5. How to run Worker:
    * `make build && ./bin/notification-service worker -t example-topic -g example-topic`
    * or alternatively `make run ARGS="worker -t example-topic -g example-topic"`
    * see more command with `make build && ./bin/notification-service worker --help`

## Notifications Feature

### Overview
The Notifications feature allows the service to store and manage user notifications. It provides API endpoints for creating notifications and retrieving them with pagination.

### Database Schema
The notifications table has the following structure:

```sql
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    description TEXT NOT NULL,
    click_action VARCHAR(50) NOT NULL,
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder BOOLEAN DEFAULT FALSE,
    notif_type_id UUID,
    notif_category_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### API Endpoints

#### Create Notification
- **Method**: POST
- **URL**: `/api/v1/notifications`
- **Request Body**:
  ```json
  {
    "user_id": "uuid",
    "description": "string",
    "click_action": "OPEN_URL",
    "click_action_url": "string",
    "is_reminder": false,
    "notif_type_id": "uuid",
    "notif_category_id": "uuid"
  }
  ```

#### Get All Notifications (with pagination)
- **Method**: GET
- **URL**: `/api/v1/notifications?page=1&limit=10`
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of items per page (default: 10, max: 100)

#### Get Notification by ID
- **Method**: GET
- **URL**: `/api/v1/notifications/{id}`

### Code Structure
The Notifications feature follows a clean architecture approach with the following components:

1. **Model** (`internal/app/model/notification.go`): Defines the Notification struct and its database mapping.

2. **Repository** (`internal/app/repository/notification_repository.go`): Handles database operations for notifications.

3. **Service** (`internal/app/service/notification_service.go`): Contains business logic for notifications.

4. **Handler** (`internal/app/handler/notification_handler.go`): Implements API endpoints for notifications.

5. **Routes** (`internal/server/route.go`): Registers notification API endpoints.

### Database Migration
To run the database migration for creating the notifications table:

```bash
# Using golang-migrate CLI
migrate -path migrations -database "postgresql://postgres:password@127.0.0.1:5432/hub_test?sslmode=disable" up

# Or manually execute the SQL
psql -h 127.0.0.1 -U postgres -d hub_test -c "CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    description TEXT NOT NULL,
    click_action VARCHAR(50) NOT NULL,
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder BOOLEAN DEFAULT FALSE,
    notif_type_id UUID,
    notif_category_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);"
```

### Prerequisites

Before running the server, make sure the following services are running:

1. **PostgreSQL**: The database server should be running in a Docker container named `postgres`.
   ```bash
   # Check if PostgreSQL is running
   docker ps | grep postgres
   ```

2. **Kafka**: The Kafka server should be running in a Docker container named `qontak-kafka-1`.
   ```bash
   # Check if Kafka is running
   docker ps | grep qontak-kafka-1
   ```

### Testing the API

After starting the server with `go run main.go server` or `make run ARGS=server`, you can test the API endpoints using curl or any API client:

```bash
# Create a notification
curl -X POST http://localhost:4000/api/v1/notifications \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "description": "New message received",
    "click_action": "OPEN_URL",
    "click_action_url": "https://example.com/messages",
    "is_reminder": false
  }'

# Get all notifications with pagination
curl http://localhost:4000/api/v1/notifications?page=1&limit=10

# Get notification by ID
curl http://localhost:4000/api/v1/notifications/123e4567-e89b-12d3-a456-************
```

### Unit Tests

The Notifications feature includes comprehensive unit tests for all components. To run the tests:

```bash
make test
```

Test coverage for the notification components:
- **Model**: 100% coverage
- **Repository**: Tests are skipped as they require a real database connection
- **Service**: 100% coverage
- **Handler**: 94.7% coverage

#### Test Structure

1. **Model Tests** (`internal/app/model/notification_test.go`): Tests for the Notification model, including TableName method, BeforeCreate hook, and BeforeUpdate hook.

2. **Repository Tests** (`internal/app/repository/notification_repository_test.go`): Tests for the NotificationRepository, currently skipped as they require a real database connection.

3. **Service Tests** (`internal/app/service/notification_service_test.go`): Tests for the NotificationService, including tests for creating notifications, retrieving by ID, and retrieving all with pagination.

4. **Handler Tests** (`internal/app/handler/notification_handler_test.go`): Tests for the NotificationHandler, including tests for all API endpoints and various error scenarios.

#### Important Notes

- Always make sure all tests pass after making changes to the codebase.
- Update test cases whenever you add, change, or delete features.

## Recent Changes

### Rollbar Removal

Rollbar error tracking has been removed from the project as it was not being actively used. This change simplifies the project dependencies and reduces potential security vulnerabilities from unused third-party libraries.

For more details, see [Rollbar Removal Documentation](docs/changes/remove-rollbar.md).