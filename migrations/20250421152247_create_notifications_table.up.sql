CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    description TEXT NOT NULL,
    click_action VARCHAR(50) NOT NULL,
    click_action_url TEXT,
    read_at TIMESTAMP,
    is_reminder <PERSON><PERSON><PERSON><PERSON>N DEFAULT FALSE,
    notif_type_id UUID,
    notif_category_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
