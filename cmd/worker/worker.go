package worker

import (
	"context"
	"fmt"
	stdLog "log"
	"log/slog"

	dbLib "bitbucket.org/mid-kelola-indonesia/go-utils/database"
	"bitbucket.org/terbang-ventures/notification-service/config"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/consumer"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/repository"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	"bitbucket.org/terbang-ventures/notification-service/internal/pkg/fcm"
	"bitbucket.org/terbang-ventures/notification-service/internal/worker"
	"bitbucket.org/terbang-ventures/notification-service/pkg/kafka"
	"github.com/IBM/sarama"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var (
	WorkerCmd = &cobra.Command{
		Use:   "worker",
		Short: "Run worker",
		Long:  "Run notification service worker",
	}
	gormDBConn         *gorm.DB
	kafkaConsumerGroup kafka.IConsumerGroup
	groupID            string
	topic              string
)

// Close all resources. For example: database connection, redis, kafka etc
func closeResource(kafkaConsumerGroup kafka.IConsumerGroup) {
	if kafkaConsumerGroup != nil {
		if err := kafkaConsumerGroup.Close(); err != nil {
			slog.Error(fmt.Sprintf("error while close kafka consumer group: %v", err))
		} else {
			slog.Info("successfully close kafka consumer group")
		}
	}
}

// Validate all entered flags
func validateFlags() {
	if groupID == "" {
		stdLog.Fatalf("group id is required")
	}

	if topic == "" {
		stdLog.Fatalf("topic is required")
	}
}

// Pre-requisite before worker running
// For example: db or redis connection, kafka config initialization, etc.
func workerPreRun(cmd *cobra.Command, args []string) error {
	validateFlags()

	config.Init()
	config.InitAppLogger(config.LoggerConf{
		ServiceName:        config.GetAppName(),
		ServiceEnvironment: config.GetAppEnv(),
	})

	gormDBConn = config.InitDatabase(dbLib.Config{
		Source:          config.GetDatabaseDsn(""),
		ConnMaxLifetime: config.GetDatabaseConnMaxLifetime(""),
		ConnMaxIdleTime: config.GetDatabaseConnMaxIdleTime(""),
		MaxOpenConns:    config.GetDatabaseMaxOpenConn(""),
		MaxIdleConns:    config.GetDatabaseMaxIdleConn(""),
	})

	kafkaConsumerGroup = config.InitKafkaConsumerGroup(kafka.Config{
		ClientID: config.GetAppName(),
		Brokers:  config.GetKafkaBrokers(),
		Sasl:     config.GetKafkaSasl(),
		Username: config.GetKafkaSaslUsername(),
		Password: config.GetKafkaSaslPassword(),
		Prefix:   config.GetKafkaPrefix(),
		Cert:     config.GetKafkaCert(),
	}, groupID)

	return nil
}

// Worker starter
// All consumers should be placed in consumer directory for each domain
func workerRun(cmd *cobra.Command, args []string) error {
	defer closeResource(kafkaConsumerGroup)

	ctx := context.Background()
	var c sarama.ConsumerGroupHandler

	if topic == worker.FCMTopic {
		// Initialize repositories
		fcmTokenRepo := repository.NewFCMTokenRepository(gormDBConn)

		// Initialize FCM service
		fcmService := fcm.NewFCMService()

		// Initialize push notification service
		pushNotificationService := service.NewPushNotificationService(fcmTokenRepo, fcmService)

		// Create FCM consumer
		c = consumer.NewFCMConsumer(pushNotificationService)
	} else if topic == worker.NotificationTopic {
		// Initialize your notification consumer here
		// Example:
		// notificationRepo := repository.NewNotificationRepository(gormDBConn)
		// notificationService := service.NewNotificationService(notificationRepo)
		// c = consumer.NewNotificationConsumer(notificationService)
		
		// Temporary placeholder until you implement the actual consumer
		c = consumer.NewNotificationConsumer()
	} else {
		stdLog.Fatalf("topic %s is not defined", topic)
	}

	w := worker.NewWorker(topic, kafkaConsumerGroup, c)
	w.Start(ctx)

	return nil
}

func init() {
	WorkerCmd.PreRunE = workerPreRun
	WorkerCmd.RunE = workerRun
	WorkerCmd.PersistentFlags().StringVarP(&groupID, "group", "g", "", "Kafka consumer groupID")
	WorkerCmd.PersistentFlags().StringVarP(&topic, "topic", "t", "", "Kafka topic")
}
