package cmd

import "github.com/spf13/cobra"

var (
	workerCmd = &cobra.Command{
		Use:   "worker",
		Short: "Run worker",
		Long:  "Run notification service worker",
	}
)

func workerPreRun(cmd *cobra.Command, args []string) error {
	// Pre-requisite before worker running
	// For example: db or redis connection, kafka config initialization, etc.

	return nil
}

func workerRun(cmd *cobra.Command, args []string) error {
	// Worker starter
	// All consumers should be placed in consumer directory for each domain

	return nil
}

func init() {
	workerCmd.PreRunE = workerPreRun
	workerCmd.RunE = workerRun
}
