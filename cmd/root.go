package cmd

import (
	"fmt"
	"os"

	"bitbucket.org/terbang-ventures/notification-service/cmd/server"
	"bitbucket.org/terbang-ventures/notification-service/cmd/worker"

	"github.com/spf13/cobra"
)

var RootCmd = &cobra.Command{
	Use:   "notification-service",
	Short: "Broadcast service",
	Long:  "Qontak chat broadcast service",
}

func Execute() {
	if err := RootCmd.Execute(); err != nil {
		_ = RootCmd.Help()
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	RootCmd.AddCommand(server.ServerCmd)
	RootCmd.AddCommand(worker.WorkerCmd)
	RootCmd.AddCommand(VersionCmd)
}
