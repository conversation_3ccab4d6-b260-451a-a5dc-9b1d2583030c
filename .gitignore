# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.out
coverage.html

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# Environment files
.env
.env.*
!.env.example

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Temporary files
tmp/
temp/

# Generated files
*.pb.go
*.pb.gw.go

# Debug files
debug
__debug_bin

# Database files
*.db
*.sqlite

# Project specific
/bin/notification-service
junit.xml